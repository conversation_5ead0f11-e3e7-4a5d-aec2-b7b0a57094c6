import 'package:json_annotation/json_annotation.dart';
import 'package:vp_socket/data/socket_data.dart';
import 'package:vp_socket/extensions/num_helper.dart';

part 'socket_market_event_data.g.dart';

@JsonSerializable()
class VPMarketEventData extends VPSocketData {
  @DynamicToStringConverter()
  final String? event;

  @DynamicToStringConverter()
  final String? session;

  const VPMarketEventData({
    required super.channel,
    this.event,
    this.session,
  });

  factory VPMarketEventData.fromJson(Map<String, dynamic> json) =>
      _$VPMarketEventDataFromJson(json);
}
