// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'socket_market_event_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VPMarketEventData _$VPMarketEventDataFromJson(Map<String, dynamic> json) =>
    VPMarketEventData(
      channel: json['channel'] as String,
      event: const DynamicToStringConverter().fromJson(json['event']),
      session: const DynamicToStringConverter().from<PERSON>son(json['session']),
    );

Map<String, dynamic> _$VPMarketEventDataToJson(VPMarketEventData instance) =>
    <String, dynamic>{
      'channel': instance.channel,
      'event': const DynamicToStringConverter().toJson(instance.event),
      'session': const DynamicToStringConverter().toJson(instance.session),
    };
