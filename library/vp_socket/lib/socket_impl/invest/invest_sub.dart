import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:vp_socket/base/subscription.dart';
import 'package:vp_socket/socket_impl/invest/invest_topic_key.dart';

part 'invest_sub.g.dart';

sealed class VPInvestSub extends VPSocketSubscription {
  const VPInvestSub({required this.channel, required this.type});

  final String channel;

  final String type;

  factory VPInvestSub.fromJson(Map<String, dynamic> json) {
    switch (json['type']) {
      case 'VPStockInfoSub':
        return VPStockInfoSub.fromJson(json);
      case 'VPMarketEventSub':
        return VPMarketEventSub.fromJson(json);
      default:
        throw ArgumentError('Unknown subtype: ${json['type']}');
    }
  }

  Map<String, dynamic> toJson();

  List<VPInvestTopicKey> toTopicKeys();
}

@JsonSerializable()
class VPStockInfoSub extends VPInvestSub {
  const VPStockInfoSub({
    required this.symbols,
    required super.channel,
    super.type = 'VPStockInfoSub',
  });

  final Set<String> symbols;

  @override
  String get subscribeData => jsonEncode(
      {"type": "sub", "channel": channel, "listId": symbols.toList()});

  @override
  String get unsubscribeData => jsonEncode(
      {"type": "unsub", "channel": channel, "listId": symbols.toList()});

  factory VPStockInfoSub.fromJson(Map<String, dynamic> json) =>
      _$VPStockInfoSubFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$VPStockInfoSubToJson(this);

  @override
  List<Object?> get props => [symbols, channel];

  @override
  List<VPInvestTopicKey> toTopicKeys() {
    return symbols
        .map((sym) => SymbolChannelTopicKey(channel: channel, symbol: sym))
        .toList();
  }
}

@JsonSerializable()
class VPMarketEventSub extends VPInvestSub {
  const VPMarketEventSub({
    required super.channel,
    super.type = 'VPMarketEventSub',
  });

  @override
  String get subscribeData => jsonEncode({"type": "sub", "channel": channel});

  @override
  String get unsubscribeData =>
      jsonEncode({"type": "unsub", "channel": channel});

  factory VPMarketEventSub.fromJson(Map<String, dynamic> json) =>
      _$VPMarketEventSubFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$VPMarketEventSubToJson(this);

  @override
  List<Object?> get props => [channel];

  @override
  List<VPInvestTopicKey> toTopicKeys() {
    return [MarketEventTopicKey(channel: channel)];
  }
}
