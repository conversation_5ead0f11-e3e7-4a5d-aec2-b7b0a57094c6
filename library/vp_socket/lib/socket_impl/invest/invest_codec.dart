import 'package:vp_socket/codec/codec.dart';
import 'package:vp_socket/data/enum/socket_channel.dart';
import 'package:vp_socket/data/socket_data.dart';
import 'package:vp_socket/socket_impl/invest/invest_sub.dart';
import 'package:vp_socket/socket_impl/invest/invest_topic_key.dart';

class VPInvestCodec extends SocketCodec<VPInvestSub, VPInvestTopicKey> {
  @override
  List<VPInvestTopicKey> toTopicKeys(VPInvestSub s) => s.toTopicKeys();

  @override
  VPInvestSub? fromTopicKeys(List<VPInvestTopicKey> keys) {
    final firstItem = keys.firstOrNull;

    switch (firstItem) {
      case MarketEventTopicKey():
        return VPMarketEventSub(
          channel: (keys.first as MarketEventTopicKey).channel,
        );

      case SymbolChannelTopicKey():
        final first = keys.first as SymbolChannelTopicKey;
        final set = <String>{};
        var ok = true;

        for (final k in keys) {
          final kk = k as SymbolChannelTopicKey;

          if (kk.channel != first.channel) {
            ok = false;
            break;
          }
          set.add(kk.symbol);
        }

        return ok ? VPStockInfoSub(symbols: set, channel: first.channel) : null;
      case null:
        return null;
    }
  }

  @override
  VPInvestTopicKey? extractKeys(VPSocketData? data) {
    final channel = data?.channel;

    final symbol = data?.symbol;

    if (channel == VPSocketChannel.marketEvent.name) {
      return MarketEventTopicKey(channel: channel!);
    }

    if (channel == null || symbol == null) return null;

    return SymbolChannelTopicKey(channel: channel, symbol: symbol);
  }
}
