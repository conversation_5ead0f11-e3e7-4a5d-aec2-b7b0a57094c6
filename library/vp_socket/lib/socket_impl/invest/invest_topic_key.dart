import 'package:vp_socket/base/topic.dart';

sealed class VPInvestTopic<PERSON>ey extends SocketTopic<PERSON>ey {}

class SymbolChannelTopic<PERSON>ey extends VPInvestTopicKey {
  final String channel;
  final String symbol;

  SymbolChannelTopicKey({required this.channel, required this.symbol});

  @override
  List<Object?> get props => [channel, symbol];

  @override
  String toString() => 'SymbolChannelTopicKey($channel/$symbol)';
}

class MarketEventTopicKey extends VPInvestTopicKey {
  final String channel;

  MarketEventTopicKey({required this.channel});

  @override
  List<Object?> get props => [channel];
}
