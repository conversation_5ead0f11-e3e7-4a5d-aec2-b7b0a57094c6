// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invest_sub.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VPStockInfoSub _$VPStockInfoSubFromJson(Map<String, dynamic> json) =>
    VPStockInfoSub(
      symbols:
          (json['symbols'] as List<dynamic>).map((e) => e as String).toSet(),
      channel: json['channel'] as String,
      type: json['type'] as String? ?? 'VPStockInfoSub',
    );

Map<String, dynamic> _$VPStockInfoSubToJson(VPStockInfoSub instance) =>
    <String, dynamic>{
      'channel': instance.channel,
      'type': instance.type,
      'symbols': instance.symbols.toList(),
    };

VPMarketEventSub _$VPMarketEventSubFromJson(Map<String, dynamic> json) =>
    VPMarketEventSub(
      channel: json['channel'] as String,
      type: json['type'] as String? ?? 'VPMarketEventSub',
    );

Map<String, dynamic> _$VPMarketEventSubToJson(VPMarketEventSub instance) =>
    <String, dynamic>{
      'channel': instance.channel,
      'type': instance.type,
    };
