import 'package:vp_socket/base/topic.dart';
import 'package:vp_socket/data/socket_data.dart';
import 'package:vp_socket/base/subscription.dart';

abstract class SocketCodec<S extends VPSocketSubscription, K extends SocketTopicKey> {
  /// Phân rã một subscription đa-symbol thành các topic key đơn lẻ để quản lý/đếm listener.
  ///
  /// - Yêu cầu: [sub] mô tả duy nhất một `channel`, có thể chứa 1..n `symbols`.
  /// - Mỗi phần tử kết quả ứng với **một symbol**.
  /// - Loại bỏ symbol trùng lặp nếu có (giữ thứ tự xuất hiện đầu tiên).
  /// - Không sửa đổi chữ hoa/thường hay chuẩn hoá symbol (caller tự đảm bảo nếu cần).
  ///
  /// ## Ví dụ
  /// Input: {channel: 'stockInfo', symbols: ['HPG', 'SSI'] }
  /// Output:
  ///   [
  ///     SocketTopicKey(channel: 'stockInfo', symbol: 'HPG'),
  ///     SocketTopicKey(channel: 'stockInfo', symbol: 'SSI'),
  ///   ]
  List<K> toTopicKeys(S sub);

  /// Gom nhiều topic key **cùng channel** thành một subscription duy nhất để sub/unsub theo lô.
  ///
  /// - Trả về `null` nếu [keys] rỗng.
  /// - Ném [ArgumentError] nếu [keys] chứa nhiều channel khác nhau.
  /// - Loại bỏ symbol trùng lặp; **giữ thứ tự ban đầu** (stable).
  /// - Kết quả thường có dạng: {channel: ..., symbols: [ ... ]}.
  ///
  /// ## Ví dụ
  /// Input:
  ///   [
  ///     SocketTopicKey(channel: 'stockInfo', symbol: 'HPG'),
  ///     SocketTopicKey(channel: 'stockInfo', symbol: 'SSI'),
  ///   ]
  /// Output:
  ///   {channel: 'stockInfo', symbols: ['HPG', 'SSI']}
  S? fromTopicKeys(List<K> keys);

  K? extractKeys(VPSocketData? data);

  Object encodeSubscribe(S sub) => sub.subscribeData;

  Object encodeUnsubscribe(S sub) => sub.unsubscribeData;
}
