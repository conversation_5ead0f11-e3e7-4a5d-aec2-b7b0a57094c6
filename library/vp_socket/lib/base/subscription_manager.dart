import 'dart:async';

import 'package:vp_socket/base/topic.dart';
import 'package:vp_socket/codec/codec.dart';
import 'package:vp_socket/data/enum/connection_state.dart';
import 'package:vp_socket/base/subscription.dart';
import 'package:vp_socket/base/base_socket.dart';
import 'package:vp_socket/socket_impl/invest/invest_topic_key.dart';

mixin SubscriptionManager<S extends VPSocketSubscription,
    K extends SocketTopicKey> on VPBaseSocket {
  late SocketCodec<S, K> _codec;

  /// Tất cả SocketTopicKey bao gồm đang active và đang pending
  final Set<K> _desiredKeys = {};

  /// SocketTopicKey đang active
  final Set<K> _appliedKeys = {};

  StreamSubscription<ConnectionStateX>? _stateSub;

  void initSubscriptionManager({required SocketCodec<S, K> codec}) {
    _codec = codec;
    _stateSub?.cancel();
    _stateSub = stateStream.listen((s) {
      if (s == ConnectionStateX.connected) {
        _appliedKeys.clear();
        _syncToWire();
      } else if (s == ConnectionStateX.reconnecting ||
          s == ConnectionStateX.closing ||
          s == ConnectionStateX.closed) {
        _appliedKeys.clear();
      }
    });
  }

  Future<void> disposeSubscriptionManager() async {
    await _stateSub?.cancel();
    _stateSub = null;
  }

  /// Đăng ký (idempotent). Trả về số lượng key **mới** được thêm vào desired.
  Future<int> subscribe(S s) async {
    final keys = _codec.toTopicKeys(s).toSet();
    // chỉ lấy phần CHƯA có trong desired
    final newDesired = keys.difference(_desiredKeys);
    if (newDesired.isEmpty) return 0; // đã có đủ → không làm gì

    // cập nhật desired trước
    _desiredKeys.addAll(newDesired);

    // nếu đã connected, chỉ gửi những key mới mà CHƯA active
    if (state == ConnectionStateX.connected) {
      final toApply = newDesired.difference(_appliedKeys);
      await _applyAddKeys(toApply);
    }
    return newDesired.length;
  }

  /// Huỷ đăng ký (idempotent). Trả về số lượng key **thực sự** bị gỡ khỏi desired.
  Future<int> unsubscribe(S s) async {
    final keys = _codec.toTopicKeys(s).toSet();

    // chỉ huỷ phần ĐANG có trong desired
    final toRemove = keys.intersection(_desiredKeys);
    if (toRemove.isEmpty) return 0;

    _desiredKeys.removeAll(toRemove);

    if (state == ConnectionStateX.connected) {
      await _applyRemoveKeys(toRemove);
    }
    return toRemove.length;
  }

  Future<void> _syncToWire() async {
    if (state != ConnectionStateX.connected) return;

    final add = _desiredKeys.difference(_appliedKeys);
    if (add.isNotEmpty) await _applyAddKeys(add);

    final del = _appliedKeys.difference(_desiredKeys);
    if (del.isNotEmpty) await _applyRemoveKeys(del);
  }

  Future<void> _applyAddKeys(Iterable<K> addKeys) async {
    if (state != ConnectionStateX.connected) return;
    final byCh = _groupByChannel(addKeys);
    for (final entry in byCh.entries) {
      final keys = entry.value;
      if (keys.isEmpty) continue;

      final sub = _codec.fromTopicKeys(keys);
      if (sub == null) continue;

      await send(_codec.encodeSubscribe(sub));
      _appliedKeys.addAll(keys); // optimistic theo đúng channel vừa gửi
    }
  }

  Future<void> _applyRemoveKeys(Iterable<K> delKeys) async {
    if (state != ConnectionStateX.connected) return;

    final byCh = _groupByChannel(delKeys);
    for (final entry in byCh.entries) {
      final keys = entry.value;
      if (keys.isEmpty) continue;

      final sub = _codec.fromTopicKeys(keys);
      if (sub == null) continue;

      await send(_codec.encodeUnsubscribe(sub));
      _appliedKeys.removeAll(keys);
    }
  }

  String _channelOf(K k) {
    if (k is SymbolChannelTopicKey) return k.channel;

    return k.runtimeType.toString();
  }

  Map<String, List<K>> _groupByChannel(Iterable<K> keys) {
    final m = <String, List<K>>{};
    for (final k in keys) {
      final ch = _channelOf(k);
      (m[ch] ??= <K>[]).add(k);
    }
    return m;
  }
}
