import 'dart:async';

import 'package:vp_socket/base/topic.dart';
import 'package:vp_socket/codec/codec.dart';
import 'package:vp_socket/data/socket_data.dart';
import 'package:vp_socket/base/subscription.dart';
import 'package:vp_socket/extensions/throttle.dart';

typedef SocketListener = void Function(VPSocketData? data);
typedef SocketSelector = bool Function(VPSocketData? prev, VPSocketData? next);

class ListenerHandle<S extends VPSocketSubscription> {
  ListenerHandle({
    required this.keys,
    required this.callback,
    required void Function() disposeImpl,
  }) : _disposeImpl = disposeImpl;

  final List<SocketTopicKey> keys; // các key đã join

  final Callback callback;

  final void Function() _disposeImpl;

  void dispose() => _disposeImpl();
}

class Callback {
  Callback({
    required this.listener,
    required this.listenWhen,
    this.throttleDuration = const Duration(milliseconds: 400),
    this.equals,
  });

  final SocketListener listener;

  final SocketSelector listenWhen;

  final Duration throttleDuration;

  final bool Function(VPSocketData? a, VPSocketData? b)? equals;

  late final Throttle _throttle = Throttle(throttleDuration);

  VPSocketData? _lastData;

  void emit(VPSocketData? data) {
    if (equals != null && equals!(_lastData, data)) return;

    if (listenWhen(_lastData, data)) {
      _lastData = data;
      _throttle.call(() => listener(data));
    }
  }
}

class _Bucket {
  VPSocketData? _last;

  final _cbs = <Callback>[];

  bool get has => _cbs.isNotEmpty;

  VPSocketData? get last => _last;

  void add(VPSocketData? d) {
    _last = d;

    for (final cb in _cbs) {
      cb.emit(d);
    }
  }

  void addCb(Callback cb, {bool emitImmediately = false}) {
    _cbs.add(cb);

    if (emitImmediately && _last != null) cb.emit(_last);
  }

  void removeBy(SocketListener l) {
    _cbs.removeWhere((e) => e.listener == l);
  }

  void dispose() {
    _cbs.clear();
  }
}

class VPSocketController<S extends VPSocketSubscription,
    K extends SocketTopicKey> {
  VPSocketController(this._codec, {this.onUnsubscribe});

  final SocketCodec<S, K> _codec;

  final void Function(S)? onUnsubscribe;

  final _streamCtrl = StreamController<VPSocketData?>.broadcast();
  late final Stream<VPSocketData?> _stream = _streamCtrl.stream;
  StreamSubscription<VPSocketData?>? _inbound;

  final _buckets = <SocketTopicKey, _Bucket>{};

  bool Function(VPSocketData? a, VPSocketData? b)? equals;

  void start() {
    _inbound ??= _stream.listen(_routeData);
  }

  void stop() {
    _inbound?.cancel();
    _inbound = null;
  }

  void add(VPSocketData? data) => _streamCtrl.add(data);

  void _routeData(VPSocketData? d) {
    final k = _codec.extractKeys(d);

    final b = _buckets[k];

    if (b == null) return;

    if (equals != null && equals!(b.last, d)) return;

    b.add(d);
  }

  ListenerHandle<S> addListener(
    S sub, {
    required SocketListener listener,
    required SocketSelector selector,
    bool emitImmediately = false,
  }) {
    start();

    final cb = Callback(
      listener: listener,
      listenWhen: selector,
      throttleDuration: Duration.zero,
    );

    final keys = _codec.toTopicKeys(sub);

    for (final k in _codec.toTopicKeys(sub)) {
      final b = _buckets.putIfAbsent(k, () => _Bucket());
      b.addCb(cb, emitImmediately: emitImmediately);
    }

    void disposeImpl() {
      final empty = <K>[];

      for (final k in keys) {
        final b = _buckets[k];

        b?.removeBy(listener);

        if (b?.has == false) {
          b?.dispose();
          _buckets.remove(k);
          empty.add(k);
        }
      }
      if (empty.isNotEmpty) {
        final unsub = _codec.fromTopicKeys(empty);
        if (unsub != null) onUnsubscribe?.call(unsub);
      }
      if (_buckets.isEmpty) stop();
    }

    return ListenerHandle<S>(
      keys: keys,
      callback: cb,
      disposeImpl: disposeImpl,
    );
  }

  void removeListener(S sub, {required SocketListener listener}) {
    final keys = _codec.toTopicKeys(sub);
    final empty = <K>[];

    for (final k in keys) {
      final b = _buckets[k];
      b?.removeBy(listener);

      if (b?.has == false) {
        b?.dispose();
        _buckets.remove(k);
        empty.add(k);
      }
    }

    if (empty.isNotEmpty) {
      final unsub = _codec.fromTopicKeys(empty);
      if (unsub != null) onUnsubscribe?.call(unsub);
    }

    if (_buckets.isEmpty) stop();
  }

  void dispose() {
    stop();

    for (final b in _buckets.values) {
      b.dispose();
    }

    _buckets.clear();
    _streamCtrl.close();
  }
}
