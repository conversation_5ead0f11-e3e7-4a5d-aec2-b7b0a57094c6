import 'package:vp_socket/vp_socket.dart';

mixin MarketSessionSocketMixin {
  VPInvestSub? topic;

  ListenerHandle? listenerHandle;

  final socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  void subscribe(Set<String>? marketCodes) {
    if (marketCodes == null || marketCodes.isEmpty) return;

    topic = VPStockInfoSub(
      symbols: marketCodes,
      channel: VPSocketChannel.marketSession.name,
    );

    socket.subscribe(topic!);

    listenerHandle = socket.addListener(
      topic!,
      listener: (data) => listener(data as VPMarketSessionData?),
      selector:
          (_, data) =>
              data is VPMarketSessionData && marketCodes.contains(data.symbol),
    );
  }

  void unsubscribe() {
    listenerHandle?.dispose();
  }

  void listener(VPMarketSessionData? data) {}
}
