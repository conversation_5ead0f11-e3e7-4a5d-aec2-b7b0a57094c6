import 'package:vp_socket/data/socket_market_event_data.dart';
import 'package:vp_socket/vp_socket.dart';

mixin MarketEventSocketMixin {
  VPInvestSub? topic;

  ListenerHandle? listenerHandle;

  final socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  void subscribeMarketEvent() {
    listenerHandle?.dispose();

    topic = VPMarketEventSub(channel: VPSocketChannel.marketEvent.name);

    socket.subscribe(topic!);

    listenerHandle = socket.addListener(
      topic!,
      listener: _onSocketMarketEventListener,
      selector: (_, data) => data is VPMarketEventData,
    );
  }

  void unsubscribeMarketEvent() {
    listenerHandle?.dispose();
    listenerHandle = null;
  }

  void _onSocketMarketEventListener(VPSocketData? data) {
    if (data is VPMarketEventData) {
      onSocketMarketEventListener(data);
    }
  }

  void onSocketMarketEventListener(VPMarketEventData data) {}
}
