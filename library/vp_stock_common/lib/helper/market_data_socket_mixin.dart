import 'package:vp_socket/vp_socket.dart';

mixin MarketDataSocketMixin {
  VPInvestSub? topic;

  ListenerHandle? listenerHandle;

  final socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  void subscribeMarketData(Set<String>? symbols) {
    if (symbols == null || symbols.isEmpty) return;

    listenerHandle?.dispose();

    topic = VPStockInfoSub(
      symbols: symbols,
      channel: VPSocketChannel.marketData.name,
    );

    socket.subscribe(topic!);

    listenerHandle = socket.addListener(
      topic!,
      listener: _onSocketMarketDataListener,
      selector:
          (_, data) =>
              data is VPMarketData &&
              symbols.contains(data.symbol) &&
              data.time != null &&
              data.closePrice != null,
    );
  }

  void unsubscribeMarketData() {
    listenerHandle?.dispose();
    listenerHandle = null;
  }

  void _onSocketMarketDataListener(VPSocketData? data) {
    if (data is VPMarketData) {
      onSocketMarketDataListener(data);
    }
  }

  void onSocketMarketDataListener(VPMarketData data) {}
}
