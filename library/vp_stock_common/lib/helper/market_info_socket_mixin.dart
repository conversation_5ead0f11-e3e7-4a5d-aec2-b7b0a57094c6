import 'package:vp_socket/vp_socket.dart';

mixin MarketInfoSocketMixin {
  VPInvestSub? topic;

  ListenerHandle? listenerHandle;

  final socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  void subscribeSocketMarketInfo(Set<String>? symbols) {
    if (symbols == null || symbols.isEmpty) return;

    listenerHandle?.dispose();

    topic = VPStockInfoSub(
      symbols: symbols,
      channel: VPSocketChannel.marketInfo.name,
    );

    socket.subscribe(topic!);

    listenerHandle = socket.addListener(
      topic!,
      listener: _onSocketMarketInfoListener,
      selector:
          (_, data) =>
              data is VPMarketInfoData && symbols.contains(data.symbol),
    );
  }

  void unsubscribeSocketMarketInfo() {
    listenerHandle?.dispose();
    listenerHandle = null;
  }

  void _onSocketMarketInfoListener(VPSocketData? data) {
    if (data is VPMarketInfoData) {
      onSocketMarketInfoListener(data);
    }
  }

  void onSocketMarketInfoListener(VPMarketInfoData data) {}
}
