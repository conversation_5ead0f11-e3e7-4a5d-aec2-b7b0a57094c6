import 'package:vp_socket/vp_socket.dart';

mixin MarketVolumeSocketMixin {
  VPInvestSub? topic;

  ListenerHandle? listenerHandle;

  final socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  void subscribeSocketMarketVolume(Set<String>? symbols) {
    if (symbols == null || symbols.isEmpty) return;

    listenerHandle?.dispose();

    topic = VPStockInfoSub(
      symbols: symbols,
      channel: VPSocketChannel.marketVolume.name,
    );

    socket.subscribe(topic!);

    listenerHandle = socket.addListener(
      topic!,
      listener: _onSocketMarketVolumeListener,
      selector:
          (_, data) =>
              data is VPMarketVolumeData && symbols.contains(data.symbol),
    );
  }

  void unsubscribeSocketMarketVolume() {
    listenerHandle?.dispose();
    listenerHandle = null;
  }

  void _onSocketMarketVolumeListener(VPSocketData? data) {
    if (data is VPMarketVolumeData) {
      onSocketMarketVolumeListener(data);
    }
  }

  void onSocketMarketVolumeListener(VPMarketVolumeData data) {}
}
