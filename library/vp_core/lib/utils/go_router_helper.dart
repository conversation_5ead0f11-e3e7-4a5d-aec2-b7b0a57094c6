import 'package:flutter/widgets.dart';
import 'package:vp_core/vp_core.dart';

extension GoRouterHelper on BuildContext {
  void popUntilRoute(String targetRoute) {
    final router = GoRouter.of(this);
    final routeMatches = router.routerDelegate.currentConfiguration.matches;

    // Ki<PERSON><PERSON> tra xem targetRoute có trong stack không
    final routeExists = routeMatches.any(
      (match) => match.matchedLocation == targetRoute,
    );

    if (routeExists) {
      // Pop cho đến khi gặp route đích
      while (router.state.matchedLocation != targetRoute) {
        if (!router.canPop()) break;
        pop();
      }
    } else {
      // Nếu route không tồn tại trong stack, điều hướng trực tiếp
      go(targetRoute);
    }
  }
}
