// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';

import 'animate_routes.dart';

class RouterUtils {
  /*----- Push normal -----*/
  static push(BuildContext context, Widget child,
      {Function(dynamic)? dataBack}) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => child),
    );
    if (dataBack != null) {
      dataBack(result);
    }
  }

  /*----- Push and Replace -----*/
  static pushReplacement(BuildContext context, Widget child,
      {Function(dynamic)? dataBack}) async {
    final result = await Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => child),
    );
    if (dataBack != null) {
      dataBack(result);
    }
  }

  /*----- Push custom animation -----*/
  static pushWithAnimation(BuildContext context, Widget child,
      {Function(dynamic)? dataBack}) async {
    final result = await Navigator.push(context, SlideRightRoute(page: child));
    if (dataBack != null) {
      dataBack(result);
    }
  }

  /*----- Push custom animation Fade -----*/
  static pushWithAnimationFade(BuildContext context, Widget child,
      {Function(dynamic)? dataBack}) async {
    final result = await Navigator.push(context, FadeRoute(page: child));
    if (dataBack != null) {
      dataBack(result);
    }
  }

  /*----- Push full screen -----*/
  static pushFullScreen(Widget child, {Function(dynamic)? onCallBack}) {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context == null) {
      return;
    }
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => child, fullscreenDialog: true),
    ).then((value) {
      if (onCallBack != null) {
        onCallBack(value);
      }
    });
  }
}
