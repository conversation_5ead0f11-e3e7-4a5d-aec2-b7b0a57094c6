import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:vp_common/extensions/date_extensions.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

enum SelectionMode {
  range,
  single,
}

extension SelectionModeExtension on SelectionMode {
  DateRangePickerSelectionMode get pickerSelectionMode {
    switch (this) {
      case SelectionMode.range:
        return DateRangePickerSelectionMode.range;
      case SelectionMode.single:
        return DateRangePickerSelectionMode.single;
    }
  }
}

enum TimeValid {
  checkWeekends,
  dateNotNull,
}

class VerticalDatePickerView extends StatefulWidget {
  const VerticalDatePickerView({
    super.key,
    this.minDate,
    this.maxDate,
    this.startDate,
    this.endDate,
    this.rangeDay,
    this.endDateAtEndOfDay = true,
    this.selectionMode = SelectionMode.range,
    this.timeValid = TimeValid.dateNotNull,
    this.widgetBodySnackBar,
  });

  final DateTime? minDate;

  final DateTime? maxDate;

  final DateTime? startDate;

  final DateTime? endDate;

  /// Số ngày cho phép chọn trước/sau ngày bắt đầu chọn
  final int? rangeDay;

  /// true: 2025-19-07 23:59:59
  /// false: 2025-19-07 00:00:00
  final bool endDateAtEndOfDay;

  final SelectionMode selectionMode;

  final TimeValid timeValid;

  final Widget? widgetBodySnackBar;

  @override
  VerticalDatePickerViewState createState() => VerticalDatePickerViewState();
}

class VerticalDatePickerViewState extends State<VerticalDatePickerView> {
  PickerDateRange? _selectedRange;

  DateTime? _minDate;

  DateTime? _maxDate;

  bool _userStartedSelection = false;

  final DateRangePickerController _controller = DateRangePickerController();

  @override
  void initState() {
    super.initState();

    // Set the initial display date for the SfDateRangePicker.
    // If a start date has already been selected (_selectedRange.startDate),
    // the picker will automatically scroll to the month containing that date
    // when opened. This improves the user experience by avoiding manual scrolling.
    if (widget.startDate != null) {
      _controller.displayDate = widget.startDate;
    }

    if (widget.startDate != null && widget.endDate != null) {
      _selectedRange = PickerDateRange(widget.startDate, widget.endDate);
    }

    _minDate = widget.minDate;
    _maxDate = widget.maxDate;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          Expanded(
            child: SfDateRangePicker(
              controller: _controller,
              selectionMode: widget.selectionMode.pickerSelectionMode,
              initialSelectedRange: _selectedRange,
              minDate: _minDate,
              maxDate: _maxDate,
              onSelectionChanged: _onSelectionChanged,
              enableMultiView: true,
              viewSpacing: 10,
              backgroundColor: vpColor.backgroundElevation0,
              navigationMode: DateRangePickerNavigationMode.scroll,
              navigationDirection: DateRangePickerNavigationDirection.vertical,
              todayHighlightColor: vpColor.strokePrimary,
              onViewChanged: (arg) {},
              selectionColor:
                  _isCheckValid ? Colors.transparent : vpColor.backgroundBrand,
              allowViewNavigation: false,
              selectionTextStyle: _isCheckValid
                  ? vpTextStyle.subtitle14?.copyWith(
                      color: vpColor.textDisabled,
                      fontWeight: FontWeight.w600,
                    )
                  : vpTextStyle.subtitle14.copyColor(
                      vpColor.textWhite,
                    ),
              startRangeSelectionColor: vpColor.backgroundBrand,
              endRangeSelectionColor: vpColor.backgroundBrand,
              rangeTextStyle: vpTextStyle.subtitle14.copyColor(
                vpColor.textPrimary,
              ),
              headerStyle: DateRangePickerHeaderStyle(
                textStyle: vpTextStyle.captionSemiBold.copyColor(
                  vpColor.textTertiary,
                ),
              ),
              rangeSelectionColor: vpColor.backgroundElevationMinus2,
              monthFormat: 'MMMM',
              monthCellStyle: (widget.timeValid == TimeValid.checkWeekends)
                  ? DateRangePickerMonthCellStyle(
                      weekendTextStyle: vpTextStyle.subtitle14?.copyWith(
                        color: vpColor.textDisabled,
                        fontWeight: FontWeight.w600,
                      ),
                    )
                  : const DateRangePickerMonthCellStyle(),
            ),
          ),
          const VPDividerView(),
          const SizedBox(height: 16),
          _VerticalDateBottomActionView(
            endDateAtEndOfDay: widget.endDateAtEndOfDay,
            selectedRange: _selectedRange,
            controller: _controller,
            onReset: _onReset,
            selectionMode: widget.selectionMode,
            timeValid: widget.timeValid,
          ),
        ],
      ),
    );
  }

  void _onSelectionChanged(DateRangePickerSelectionChangedArgs args) {
    DateTime? dateChoose;
    if (widget.selectionMode == SelectionMode.single) {
      dateChoose = args.value as DateTime?;
    }
    final range = (widget.selectionMode == SelectionMode.range)
        ? (args.value as PickerDateRange?)
        : PickerDateRange(dateChoose, dateChoose);

    if (range == null) {
      setState(() => _selectedRange = range);

      return;
    }

    if (!_userStartedSelection &&
        range.startDate != null &&
        widget.rangeDay != null) {
      final baseDate = range.startDate!;
      setState(() {
        _userStartedSelection = true;
        _minDate = baseDate.subtract(Duration(days: widget.rangeDay!));
        _maxDate = baseDate.add(Duration(days: widget.rangeDay!));
      });
    }

    if (range.startDate != null) {
      setState(() => _selectedRange = range);
    }

    ///Show cảnh báo theo timeValid
    _showWarning();
  }

  void customShowSnackBar() {
    hideSnackBar();
    final snackBar = SnackBar(
      content: widget.widgetBodySnackBar ?? const SizedBox(),
      backgroundColor: Colors.transparent,
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 2),
      elevation: 0,
      dismissDirection: DismissDirection.horizontal,
      width: MediaQuery.sizeOf(context).width,
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  void hideSnackBar() {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
  }

  void _showWarning() {
    switch (widget.timeValid) {
      case TimeValid.checkWeekends:
        if (widget.widgetBodySnackBar != null && _isCheckValid) {
          customShowSnackBar();
        } else {
          hideSnackBar();
        }
        return;
      case TimeValid.dateNotNull:
        return;
    }
  }

  bool get _isCheckValid {
    switch (widget.timeValid) {
      case TimeValid.checkWeekends:
        return _selectedRange?.startDate?.weekday == DateTime.saturday ||
            _selectedRange?.startDate?.weekday == DateTime.sunday;
      case TimeValid.dateNotNull:
        return false;
    }
  }

  void _onReset() {
    setState(() {
      _resetting();
      _userStartedSelection = false;
      _minDate = widget.minDate;
      _maxDate = widget.maxDate;
    });
  }

  void _resetting() {
    switch (widget.selectionMode) {
      case SelectionMode.single:
        _controller.selectedDate = null;
        return;
      case SelectionMode.range:
        _controller.selectedRange = null;
        return;
    }
  }
}

class _VerticalDateBottomActionView extends StatelessWidget {
  const _VerticalDateBottomActionView({
    required this.selectedRange,
    required this.controller,
    this.endDateAtEndOfDay = true,
    this.onReset,
    this.selectionMode = SelectionMode.range,
    this.timeValid = TimeValid.dateNotNull,
  });

  final VoidCallback? onReset;

  final PickerDateRange? selectedRange;

  final DateRangePickerController controller;

  /// true: 2025-19-07 23:59:59
  /// false: 2025-19-07 00:00:00
  final bool endDateAtEndOfDay;

  final SelectionMode selectionMode;

  final TimeValid timeValid;

  bool get _timeValid {
    switch (timeValid) {
      case TimeValid.checkWeekends:
        return selectedRange?.startDate?.weekday != DateTime.saturday &&
            selectedRange?.startDate?.weekday != DateTime.sunday;
      case TimeValid.dateNotNull:
        return selectedRange?.startDate != null &&
            selectedRange?.endDate != null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _timeValid ? buildTimeView() : const SizedBox.shrink()),

        const SizedBox(width: 15),

        /// reset button
        VpsButton.teriatySmall(
          title: 'Đặt lại',
          onPressed: onReset,
        ),

        const SizedBox(width: 32),

        /// apply button
        VpsButton.primarySmall(
          title: 'Áp dụng',
          disabled: !_timeValid,
          onPressed: () => Navigator.of(context).pop(
            (
              startDate: selectedRange?.startDate,
              endDate: endDateAtEndOfDay
                  ? selectedRange?.endDate
                      ?.copyWith(hour: 23, minute: 59, second: 59)
                  : selectedRange?.endDate,
            ),
          ),
        ),
      ],
    );
  }

  Widget buildTimeView() {
    final startDate = selectedRange?.startDate?.formatToDdMmYyyy();
    final endDate = selectedRange?.endDate?.formatToDdMmYyyy();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ngày chọn',
          style: vpTextStyle.captionRegular.copyColor(
            vpColor.textSecondary,
          ),
        ),
        Text(
          _dateDisplay(startDate, endDate),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
        ),
      ],
    );
  }

  String _dateDisplay(String? startDate, String? endDate) {
    switch (selectionMode) {
      case SelectionMode.single:
        return startDate ?? '';
      case SelectionMode.range:
        return '$startDate - $endDate';
    }
  }
}
