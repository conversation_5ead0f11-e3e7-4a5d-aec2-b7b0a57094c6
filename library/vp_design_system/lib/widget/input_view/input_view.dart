import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_design_system/gen/assets.gen.dart';
import 'package:vp_design_system/themes/utils.dart';

typedef IconColor = Widget? Function(Color? color);
typedef WidgetColor = Widget? Function(Color? color);

enum LabelType { labelDefault, float }

enum InputType {
  rest,
  error,
  success,
  warning,
  disabled,
  typing;

  TextStyle? getTextStyle(BuildContext context) {
    return context.textStyle.body14?.copyWith(color: getTextColor(context));
  }

  Color? getPrefixIconColor(BuildContext context) {
    switch (this) {
      case rest:
        return context.colors.iconPrimary;

      case error:
        return context.colors.iconPrimary;

      case success:
        return context.colors.iconPrimary;

      case warning:
        return context.colors.iconPrimary;

      case disabled:
        return context.colors.iconDisabled;

      case typing:
        return context.colors.iconPrimary;
    }
  }

  Color? getSuffixIconColor(BuildContext context) {
    switch (this) {
      case rest:
        return context.colors.iconPrimary;

      case error:
        return context.colors.iconPrimary;

      case success:
        return context.colors.iconPrimary;

      case warning:
        return context.colors.iconPrimary;

      case disabled:
        return context.colors.iconDisabled;

      case typing:
        return context.colors.iconPrimary;
    }
  }

  Color getTextColor(BuildContext context) {
    switch (this) {
      case rest:
        return context.colors.textPrimary;

      case error:
        return context.colors.textPrimary;

      case success:
        return context.colors.textPrimary;

      case warning:
        return context.colors.textPrimary;

      case disabled:
        return context.colors.textDisabled;

      case typing:
        return context.colors.textPrimary;
    }
  }

  Color getCursorColor(BuildContext context) {
    switch (this) {
      case rest:
        return context.colors.textPrimary;

      case error:
        return context.colors.textPrimary;

      case success:
        return context.colors.textPrimary;

      case warning:
        return context.colors.textPrimary;

      case disabled:
        return context.colors.textPrimary;

      case typing:
        return context.colors.textPrimary;
    }
  }

  Color getBorderColor(BuildContext context) {
    switch (this) {
      case rest:
        return context.colors.strokeNormal;

      case error:
        return context.colors.strokeDanger;

      case success:
        return context.colors.strokePrimary;

      case warning:
        return context.colors.strokeWarning;

      case disabled:
        return context.colors.strokeDisable;

      case typing:
        return context.colors.strokeGray;
    }
  }

  Color getBackgroundColor(BuildContext context) {
    switch (this) {
      case rest:
        return context.colors.backgroundElevation0;

      case error:
        return context.colors.backgroundAccentRed;

      case success:
        return context.colors.backgroundAccentGreen;

      case warning:
        return context.colors.backgroundAccentYellow;

      case disabled:
        return context.colors.backgroundElevationMinus2;

      case typing:
        return context.colors.backgroundElevation0;
    }
  }

  Color getLabelColor(BuildContext context) {
    switch (this) {
      case rest:
        return context.colors.textPrimary;

      case error:
        return context.colors.textPrimary;

      case success:
        return context.colors.textPrimary;

      case warning:
        return context.colors.textPrimary;

      case disabled:
        return context.colors.textDisabled;

      case typing:
        return context.colors.textPrimary;
    }
  }

  Color getCaptionColor(BuildContext context) {
    switch (this) {
      case rest:
        return context.colors.textSecondary;

      case error:
        return context.colors.textAccentRed;

      case success:
        return context.colors.textAccentGreen;

      case warning:
        return context.colors.textAccentYellow;

      case disabled:
        return context.colors.textDisabled;

      case typing:
        return context.colors.textSecondary;
    }
  }

  Color getHintColor(BuildContext context) {
    switch (this) {
      case rest:
        return context.colors.textTertiary;

      case error:
        return context.colors.textPrimary;

      case success:
        return context.colors.textPrimary;

      case warning:
        return context.colors.textPrimary;

      case disabled:
        return context.colors.textDisabled;

      case typing:
        return context.colors.textPrimary;
    }
  }

  InputDecoration getInputDecoration(
    BuildContext context, {
    BorderRadius? borderRadius,
  }) {
    final border = OutlineInputBorder(
      borderSide: BorderSide(
        color: getBorderColor(context),
      ),
      borderRadius: borderRadius ?? BorderRadius.circular(8),
    );

    return InputDecoration(
      border: border,
      errorBorder: border,
      enabledBorder: border,
      focusedBorder: border,
      focusedErrorBorder: border,
      disabledBorder: border,
      hintStyle: context.textStyle.body14?.copyWith(
        color: getHintColor(context),
      ),
      filled: true,
      fillColor: getBackgroundColor(context),
    );
  }
}

class VPTextField extends StatefulWidget {
  const VPTextField({
    super.key,
    this.controller,
    this.focusNode,
    this.decoration,
    this.keyboardType,
    this.textInputAction,
    this.style,
    this.textAlign = TextAlign.start,
    this.autofocus = false,
    this.obscureText = false,
    this.autocorrect = false,
    this.enableSuggestions = false,
    this.maxLines,
    this.minLines,
    this.readOnly = false,
    this.maxLength,
    this.onChanged,
    this.onEditingComplete,
    this.onSubmitted,
    this.inputFormatters,
    this.cursorColor,
    this.onTap,
    this.hintText,
    this.isDense = false,
    this.contentPadding = const EdgeInsets.fromLTRB(16, 8, 16, 8),
    this.suffixIcon,
    this.prefixIcon,
    this.caption,
    this.captionText,
    this.label,
    this.labelText,
    this.labelStyle,
    this.captionStyle,
    this.iconShowPassword,
    this.iconHidePassword,
    this.inputType = InputType.rest,
    this.hintStyle,
    this.filledColor,
    this.borderRadius,
    this.labelPadding = const EdgeInsets.fromLTRB(0, 0, 0, 4),
    this.captionPadding,
    this.validator,
    this.floatingLabelStyle,
    this.counterText = '',
    this.labelType = LabelType.labelDefault,
    this.enableInteractiveSelection,
    this.initialValue,
    this.suffixIconConstraints,
  });

  const VPTextField.large({
    super.key,
    this.controller,
    this.focusNode,
    this.decoration,
    this.keyboardType,
    this.textInputAction,
    this.style,
    this.textAlign = TextAlign.start,
    this.autofocus = false,
    this.obscureText = false,
    this.autocorrect = false,
    this.enableSuggestions = false,
    this.maxLines,
    this.minLines,
    this.readOnly = false,
    this.maxLength,
    this.onChanged,
    this.onEditingComplete,
    this.onSubmitted,
    this.inputFormatters,
    this.cursorColor,
    this.onTap,
    this.hintText,
    this.suffixIcon,
    this.prefixIcon,
    this.caption,
    this.captionText,
    this.isDense = true,
    this.label,
    this.labelText,
    this.labelStyle,
    this.captionStyle,
    this.inputType = InputType.rest,
    this.iconShowPassword,
    this.iconHidePassword,
    this.hintStyle,
    this.filledColor,
    this.borderRadius,
    this.captionPadding,
    this.labelPadding = const EdgeInsets.fromLTRB(0, 0, 0, 4),
    this.contentPadding = const EdgeInsets.fromLTRB(16, 16, 16, 16),
    this.validator,
    this.floatingLabelStyle,
    this.counterText = '',
    this.labelType = LabelType.labelDefault,
    this.enableInteractiveSelection,
    this.initialValue,
    this.suffixIconConstraints,
  });

  const VPTextField.medium({
    super.key,
    this.controller,
    this.focusNode,
    this.decoration,
    this.keyboardType,
    this.textInputAction,
    this.style,
    this.textAlign = TextAlign.start,
    this.autofocus = false,
    this.obscureText = false,
    this.autocorrect = false,
    this.enableSuggestions = false,
    this.maxLines,
    this.minLines,
    this.readOnly = false,
    this.maxLength,
    this.onChanged,
    this.onEditingComplete,
    this.onSubmitted,
    this.inputFormatters,
    this.cursorColor,
    this.onTap,
    this.hintText,
    this.suffixIcon,
    this.prefixIcon,
    this.caption,
    this.captionText,
    this.isDense = true,
    this.label,
    this.labelText,
    this.labelStyle,
    this.captionStyle,
    this.inputType = InputType.rest,
    this.iconShowPassword,
    this.iconHidePassword,
    this.hintStyle,
    this.filledColor,
    this.borderRadius,
    this.labelPadding = const EdgeInsets.fromLTRB(0, 0, 0, 4),
    this.contentPadding = const EdgeInsets.fromLTRB(16, 8, 16, 8),
    this.captionPadding,
    this.validator,
    this.floatingLabelStyle,
    this.counterText = '',
    this.labelType = LabelType.labelDefault,
    this.enableInteractiveSelection,
    this.initialValue,
    this.suffixIconConstraints,
  });

  const VPTextField.small({
    super.key,
    this.controller,
    this.focusNode,
    this.decoration,
    this.keyboardType,
    this.textInputAction,
    this.style,
    this.textAlign = TextAlign.start,
    this.autofocus = false,
    this.obscureText = false,
    this.autocorrect = false,
    this.enableSuggestions = false,
    this.maxLines,
    this.minLines,
    this.readOnly = false,
    this.maxLength,
    this.onChanged,
    this.onEditingComplete,
    this.onSubmitted,
    this.inputFormatters,
    this.cursorColor,
    this.onTap,
    this.hintText,
    this.suffixIcon,
    this.prefixIcon,
    this.isDense = true,
    this.label,
    this.labelText,
    this.labelPadding = const EdgeInsets.fromLTRB(0, 0, 0, 4),
    this.caption,
    this.captionText,
    this.labelStyle,
    this.captionStyle,
    this.inputType = InputType.rest,
    this.iconShowPassword,
    this.iconHidePassword,
    this.hintStyle,
    this.filledColor,
    this.borderRadius,
    this.contentPadding = const EdgeInsets.fromLTRB(8, 4, 8, 4),
    this.captionPadding,
    this.validator,
    this.floatingLabelStyle,
    this.counterText = '',
    this.labelType = LabelType.labelDefault,
    this.enableInteractiveSelection,
    this.initialValue,
    this.suffixIconConstraints,
  });

  final TextEditingController? controller;

  final FocusNode? focusNode;

  final InputDecoration? decoration;

  final TextInputType? keyboardType;

  final TextInputAction? textInputAction;

  final TextStyle? style;

  final TextAlign textAlign;

  final bool autofocus;

  final bool obscureText;

  final bool autocorrect;

  final bool enableSuggestions;

  final int? maxLines;

  final int? minLines;

  final bool readOnly;

  final int? maxLength;

  final ValueChanged<String>? onChanged;

  final VoidCallback? onEditingComplete;

  final ValueChanged<String>? onSubmitted;

  final List<TextInputFormatter>? inputFormatters;

  final Color? cursorColor;

  final GestureTapCallback? onTap;

  final String? hintText;

  final bool isDense;

  final EdgeInsets contentPadding;

  final IconColor? suffixIcon;

  final IconColor? prefixIcon;

  final WidgetColor? caption;

  final String? captionText;

  final TextStyle? captionStyle;

  final EdgeInsets? captionPadding;

  final WidgetColor? label;

  final String? labelText;

  final TextStyle? labelStyle;

  final TextStyle? floatingLabelStyle;

  final EdgeInsets? labelPadding;

  final InputType inputType;

  final IconColor? iconShowPassword;

  final IconColor? iconHidePassword;

  final TextStyle? hintStyle;

  final BorderRadius? borderRadius;

  final Color? filledColor;

  final FormFieldValidator<String>? validator;

  final LabelType labelType;

  final String counterText;

  final bool? enableInteractiveSelection;

  final String? initialValue;

  final BoxConstraints? suffixIconConstraints;

  @override
  State<VPTextField> createState() => VPTextFieldState();
}

class VPTextFieldState extends State<VPTextField> {
  late InputType inputType = widget.inputType;

  late FocusNode focusNode = widget.focusNode ?? FocusNode();

  late bool obscureText = widget.obscureText;

  bool get enabled => inputType != InputType.disabled;

  Color? get suffixIconColor => inputType.getSuffixIconColor(context);

  Color? get prefixIconColor => inputType.getPrefixIconColor(context);

  bool get isLabelFloat => widget.labelType == LabelType.float;

  bool get hasLabel =>
      widget.labelType == LabelType.labelDefault &&
      (widget.label != null ||
          (widget.labelText != null && widget.labelText!.isNotEmpty));

  bool get hasCaption =>
      widget.caption != null ||
      (widget.captionText != null && widget.captionText!.isNotEmpty);

  TextStyle? get labelStyle =>
      widget.labelStyle ??
      context.textStyle.subtitle14
          ?.copyWith(color: inputType.getLabelColor(context));

  @override
  void didUpdateWidget(covariant VPTextField oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.inputType != widget.inputType) {
      inputType = widget.inputType;
    }
  }

  void onFocusListener() {
    if (!context.mounted) return;

    if (inputType == InputType.error || inputType == InputType.disabled) return;

    if (focusNode.hasFocus) {
      if (inputType == InputType.typing) return;

      setState(() => inputType = InputType.typing);
    } else if (inputType != InputType.rest) {
      setState(() => inputType = InputType.rest);
    }
  }

  @override
  void initState() {
    super.initState();

    focusNode.addListener(onFocusListener);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      focusNode
        ..removeListener(onFocusListener)
        ..dispose();
    }

    super.dispose();
  }

  Widget? buildSuffixIcon() {
    if (!widget.obscureText) return widget.suffixIcon?.call(suffixIconColor);

    if (obscureText) {
      return IconButton(
          onPressed: () => setState(() => obscureText = false),
          icon: widget.iconShowPassword?.call(suffixIconColor) ??
              Assets.icons.icHideText.svg(
                colorFilter: ColorFilter.mode(
                  context.colors.iconPrimary,
                  BlendMode.srcIn,
                ),
              )
          // SvgPicture.asset(
          //   package: v
          //   Assets.icons.icHideText.path,
          // )
          // Icon(
          //   CupertinoIcons.eye,
          //   color: suffixIconColor,
          //   size: 24,
          // ),
          );
    }

    return IconButton(
      onPressed: () => setState(() => obscureText = true),
      icon: widget.iconHidePassword?.call(suffixIconColor) ??
          Assets.icons.icShowText.svg(
            colorFilter: ColorFilter.mode(
              context.colors.iconPrimary,
              BlendMode.srcIn,
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final child = TextFormField(
      decoration: inputType
          .getInputDecoration(context, borderRadius: widget.borderRadius)
          .copyWith(
            hintText: widget.hintText,
            isDense: widget.isDense,
            contentPadding: widget.contentPadding,
            suffixIcon: buildSuffixIcon(),
            prefixIcon: widget.prefixIcon?.call(prefixIconColor),
            suffixIconColor: suffixIconColor,
            prefixIconColor: prefixIconColor,
            hintStyle: widget.hintStyle,
            fillColor: widget.filledColor,
            labelText: isLabelFloat ? widget.labelText : null,
            labelStyle: isLabelFloat ? labelStyle : null,
            floatingLabelStyle: isLabelFloat ? widget.floatingLabelStyle : null,
            label: isLabelFloat
                ? widget.label?.call(inputType.getLabelColor(context))
                : null,
            error: buildCaptionView(),
            errorStyle: widget.captionStyle,
            counterText: widget.counterText,
            suffixIconConstraints: widget.suffixIconConstraints,
          ),
      initialValue: widget.initialValue,
      enableInteractiveSelection: widget.enableInteractiveSelection,
      controller: widget.controller,
      focusNode: focusNode,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      style: widget.style ?? inputType.getTextStyle(context),
      textAlign: widget.textAlign,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      obscureText: obscureText,
      autocorrect: widget.autocorrect,
      enableSuggestions: widget.enableSuggestions,
      maxLines: obscureText ? 1 : widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      onChanged: widget.onChanged,
      onEditingComplete: widget.onEditingComplete,
      onFieldSubmitted: widget.onSubmitted,
      inputFormatters: widget.inputFormatters,
      enabled: enabled,
      cursorColor: widget.cursorColor ?? inputType.getCursorColor(context),
      onTap: widget.onTap,
      validator: widget.validator,
    );

    return !hasLabel
        ? child
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [buildLabelView(), child],
          );
  }

  Widget? buildCaptionView() {
    if (!hasCaption) return null;

    var child = widget.caption?.call(inputType.getCaptionColor(context)) ??
        Text(
          widget.captionText ?? '',
          style: widget.captionStyle ??
              context.textStyle.captionRegular?.copyWith(
                color: inputType.getCaptionColor(context),
              ),
        );

    child = Transform.translate(
      offset: Offset(-widget.contentPadding.left, 0),
      child: child,
    );

    if (widget.captionPadding == null) return child;

    return Padding(padding: widget.captionPadding!, child: child);
  }

  Widget buildLabelView() {
    final child = widget.label?.call(inputType.getLabelColor(context)) ??
        Text(widget.labelText ?? '', style: labelStyle);

    if (widget.labelPadding == null) return child;

    return Padding(padding: widget.labelPadding!, child: child);
  }
}
