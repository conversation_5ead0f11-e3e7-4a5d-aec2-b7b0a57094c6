import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_centralize/helper/utils.dart';
import 'package:vp_centralize/interceptor/fss_centralize_interceptor.dart';
import 'package:vp_centralize/interceptor/handle_unauthorized_interceptor.dart';
import 'package:vp_centralize/interceptor/retry_connection_interceptor.dart';
import 'package:vp_centralize/model/params/get_smart_otp_params.dart';
import 'package:vp_centralize/model/params/get_sms_otp_params.dart';
import 'package:vp_centralize/model/params/pin_params.dart';
import 'package:vp_centralize/router/centralize_router.dart';
import 'package:vp_centralize/screen/smart_otp/settings_smart_otp/smart_otp_register_helper/smart_otp_helper.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/interceptor/session_interceptor.dart';
import 'package:vp_core/vp_core.dart';

bool is2FA(dynamic data) {
  if (data is! Map) return false;

  return data['code'] == needVerify2FA;
}

class IAMCentralizeInterceptor extends InterceptorsWrapper {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    _handle2FA(err, handler);
  }

  OTPVerifyBy getOTPVerifyBy(String method2FA) {
    switch (method2FA) {
      case 'email':
        return OTPVerifyBy.email;
      case 'otp':
        return OTPVerifyBy.otp;
      case 'sms':
        return OTPVerifyBy.sms;
      default:
        return OTPVerifyBy.sms;
    }
  }

  void _handle2FA(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == HttpStatus.badRequest &&
        is2FA(err.response?.data)) {
      try {
        final response = BaseResponse.fromJson(
          err.response?.data,
          (json) => json,
        );

        final method2FA = (response.data as dynamic)['method'];

        final transactionId = (response.data as dynamic)['transactionId'];

        final expireSeconds =
            (response.data as dynamic)['expireSeconds'] ?? 180;

        final allowKeepSession =
            (response.data as dynamic)['allowKeepSession'] ?? false;

        final options = err.requestOptions;

        if (!getContext.mounted) return;

        switch (method2FA) {
          case 'email':
          case 'otp':
          case 'sms':
            final result = await getContext.push(
              CentralizeRouter.sms.routeName,
              extra: GetSmsOTPParams(
                isCentralize: true,
                verifyBy: getOTPVerifyBy(method2FA),
                onResendSmsOTP: (_, keepSession) {
                  return retryOn2FA(
                    err.requestOptions,
                    null,
                    method2FA,
                    keepSession: keepSession,
                    transactionId: transactionId,
                  );
                },
                onSubmitPinCode: (code, _, keepSession, resendOTPResponse) {
                  return retryOn2FA(
                    err.requestOptions,
                    code,
                    method2FA,
                    keepSession: keepSession,
                    transactionId:
                        CentralizeUtils().getTransactionId(resendOTPResponse) ??
                        transactionId,
                  );
                },
                expireSeconds: expireSeconds,
                allowKeepSession: allowKeepSession,
              ),
            );

            _handleResult(
              result: result,
              options: options,
              errorHandler: handler,
            );
            break;
          case 'pin':
            final result = await getContext.push(
              CentralizeRouter.pin.routeName,
              extra: GetPinParams(
                isCentralize: true,
                onSubmitPinCode: (code, method, keepSession, _) {
                  return retryOn2FA(
                    err.requestOptions,
                    code,
                    method,
                    keepSession: keepSession,
                    transactionId: transactionId,
                  );
                },
                allowKeepSession: allowKeepSession,
              ),
            );

            _handleResult(
              result: result,
              options: options,
              errorHandler: handler,
            );

            break;
          case 'smart':
            final status = await SmartOTPHelper().getSmartOTPStatus();

            if (status == null) return;

            final isRegistered =
                status.item1 == SmartOTPStatus.registerInThisDevice;

            final inOtherDevice =
                status.item1 == SmartOTPStatus.registerInOtherDevice;

            if (isRegistered) {
              bool keepSession = false;
              if (!getContext.mounted) return;
              final result = await getContext.push(
                CentralizeRouter.smartOTP.routeName,
                extra: GetSmartOTPParams(
                  allowKeepSession: allowKeepSession,
                  onKeepSessionChanged: (value) => keepSession = value,
                ),
              );

              if (result is String) {
                final tuple3 = await retryOn2FA(
                  err.requestOptions,
                  result,
                  method2FA,
                  keepSession: keepSession,
                  transactionId: transactionId,
                );

                final isSuccess = tuple3.item1;
                final responseApi = tuple3.item3;

                _handleResult(
                  options: options,
                  errorHandler: handler,
                  result: Tuple2(isSuccess, responseApi),
                );
              } else {
                _handleResult(
                  result: null,
                  options: options,
                  errorHandler: handler,
                );
              }
            } else {
              _handleAuthWithSmartOTP(
                err,
                handler,
                deviceRegistered: status.item2,
                registerInOtherDevice: inOtherDevice,
              );
            }
            break;
          default:
            handler.next(err);
            break;
        }
      } catch (e, stackTrace) {
        debugPrintStack(stackTrace: stackTrace);
        handler.next(err);
      }
    } else {
      handler.next(err);
    }
  }

  void _handleAuthWithSmartOTP(
    DioException err,
    ErrorInterceptorHandler handler, {
    bool registerInOtherDevice = false,
    String? deviceRegistered,
  }) async {
    _handleResult(
      result: Response(
        data: {'status': smartOTPUnRegistered, 's': smartOTPUnRegistered},
        requestOptions: err.requestOptions,
      ),
      errorHandler: handler,
      options: err.requestOptions,
    );

    /// prevent dialog is popped when repository hideLoading
    await Future.delayed(const Duration(milliseconds: 300));

    showUnRegisterSmartOTPDialogType1(
      registerInOtherDevice: registerInOtherDevice,
      deviceRegistered: deviceRegistered,
    );
  }

  void _handleResult({
    required dynamic result,
    required RequestOptions options,
    ErrorInterceptorHandler? errorHandler,
  }) {
    late Response response;

    if (result is Tuple2<bool, Response?>) {
      response = result.item2!;
    } else if (result is Response) {
      response = result;
    } else {
      /// handle for case user press back button from input OTP page
      response = Response(
        data: {'status': closeInputOTPErrorCode, 's': closeInputOTPErrorCode},
        requestOptions: options,
      );
    }

    errorHandler?.resolve(response);
  }

  /// T1: isSuccess
  /// T2: Error message if any
  /// T3: response from api
  ///
  /// code == null when resend SMS OTP
  Future<Tuple3<bool, String?, Response?>> retryOn2FA(
    RequestOptions requestOptions,
    String? code,
    String method, {
    bool keepSession = false,
    String? transactionId,
  }) async {
    try {
      Dio dio = Dio();

      dio.interceptors.addAll([
        /// Add Bearer token to Authorization
        SessionInterceptor(),

        /// handle error 401, maintenance system
        HandleUnauthorizedInterceptor(),

        /// retry connection
        RetryConnectionChangeInterceptor(),

        /// Show log at Logging screen
        LogAPIInterceptor(),
        // DerivativeInterceptor(),
        // LoggingInterceptor(),
      ]);

      requestOptions.headers.addAll({
        'value': code,
        'method': method,
        'transaction_id': transactionId,
        'keep_session': keepSession,
      });

      /// To-do: tạm thời
      final originalBaseUrl = requestOptions.baseUrl;
      if (requestOptions.baseUrl != 'https://neopro-uat.vpbanks.com.vn') {
        requestOptions.baseUrl = 'https://neopro-uat.vpbanks.com.vn';
      }
      final expectPath = [
        '/iam/external/users/bank-account',
        '/iam/external/users/bank-account/bank-bond',
        '/iam/external/users/bank-account/bank-fund',
        '/iam/external/account',
      ];
      if (expectPath.any((path) => path == requestOptions.path)) {
        requestOptions.baseUrl = originalBaseUrl;
      }

      final data = requestOptions.data;

      if (data is Map && data['centralize_method'] == true) {
        data.remove('centralize_method');
        data['method'] = method;
      }

      final response = await dio.fetch(requestOptions);

      final result = BaseResponse.fromJson(response.data, (json) => json);

      if (result.isSuccess) {
        return Tuple3(true, null, response);
      } else {
        return Tuple3(false, result.message, response);
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      return handleApiError(e);
    }
  }

  Future<Tuple3<bool, String?, Response?>> handleApiError(Object e) async {
    final error = HandleError.from(e);
    final errorMessage = await getErrorMessage(error);

    if (e is DioException && error is ResponseError) {
      final response = Response(
        data: e.response?.data,
        requestOptions: e.requestOptions,
      );

      return Tuple3(false, errorMessage, response);
    } else {
      return Tuple3(false, errorMessage, null);
    }
  }
}
