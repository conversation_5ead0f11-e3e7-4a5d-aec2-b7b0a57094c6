import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_settings/generated/l10n.dart';
import 'package:vp_settings/screen/customer_info/signature/app_signature_camera/button_widget.dart';
import '../../../../gen/assets.gen.dart' as asset;
import 'package:vp_settings/cubit/customer_info/app_signature_camera/app_camera_signature_bloc.dart';
import 'package:vp_settings/screen/customer_info/signature/app_signature_camera/rectangle_painter.dart';

class AppCameraSignaturePage extends StatefulWidget {
  const AppCameraSignaturePage({
    super.key,
    required this.camera,
    this.title,
    this.nameFileSave,
    this.onCallBackUse,
  });

  final CameraDescription camera;
  final String? title;
  final String? nameFileSave;
  final Function(dynamic)? onCallBackUse;

  @override
  State<AppCameraSignaturePage> createState() => AppCameraSignaturePageState();
}

class AppCameraSignaturePageState extends State<AppCameraSignaturePage> {
  late CameraController controller;
  late Future<void> initiallizeControllerFuture;
  final bloc = AppCameraSignatureBloc();
  bool havePathImage = false;
  String pathSave = '';

  @override
  void initState() {
    super.initState();

    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    controller = CameraController(
      widget.camera,
      ResolutionPreset.high,
      enableAudio: false,
    );

    initiallizeControllerFuture =
        controller.initialize()..then((_) {
          controller.lockCaptureOrientation(DeviceOrientation.portraitUp);
        });

    bloc.controllerData.stream.listen((pathSave) {
      if (pathSave.isNotEmpty) {
        setState(() {
          havePathImage = true;
          this.pathSave = pathSave;
          showAcceptImageBottomSheet(context);
        });
      } else {
        showMessage(
          'Chụp ảnh không thành công. Vui lòng kiểm tra lại quyền truy cập Camera của thiết bị',
        );
      }
    });
  }

  @override
  void dispose() {
    controller.dispose();
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.black87,
        appBar: buildAppBar(context),
        body: buildCameraView(),
      ),
    );
  }

  /*-------- Build AppBar -------- */
  AppBar buildAppBar(BuildContext context) {
    return AppBar(
      title: Text(
        widget.title ?? '',
        style: vpTextStyle.headineBold6.copyColor(Colors.white),
      ),
      centerTitle: true,
      backgroundColor: Colors.black,
      leading: const SizedBox(),
    );
  }

  /*-------- Build CameraView -------- */
  FutureBuilder<void> buildCameraView() {
    return FutureBuilder<void>(
      future: initiallizeControllerFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          final cameraPreview = CameraPreview(controller);

          return Stack(
            children: [
              SizedBox(
                width: controller.value.previewSize?.width,
                height: controller.value.previewSize?.height,
                child:
                    havePathImage ? Image.file(File(pathSave)) : cameraPreview,
              ),
              CustomPaint(painter: RectanglePainter(), child: Container()),
              ColoredBox(
                color: Colors.black,
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Text(
                    VPSettingsLocalize.current.settings_tutorial_OCR2,
                    style: vpTextStyle.body14.copyColor(Colors.white),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              buildBottomCamera(),

              // buildLoading()
            ],
          );
        } else {
          return const Center(child: CircularProgressIndicator());
        }
      },
    );
  }

  /*-------- Build Bottom View -------- */
  Align buildBottomCamera() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: double.infinity,
        height: 100,
        color: Colors.black,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.center,
              child: InkWell(
                onTap: () {
                  captureImage();
                },
                child: SvgPicture.asset(asset.Assets.icons.icBtnCamera),
              ),
            ),
            Align(
              alignment: Alignment.centerLeft,
              child: TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  VPSettingsLocalize.current.settings_cancel,
                  style: vpTextStyle.headine6.copyColor(themeData.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /*-------- Build ViewLoadingAndDialog -------- */
  StreamBuilder buildLoading() {
    return StreamBuilder<bool>(
      stream: bloc.controllerLoading.stream,
      builder: (context, snapshot) {
        final isShowLoading = snapshot.data ?? false;
        if (isShowLoading) {
          return Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            color: Colors.black.withOpacity(0.7),
            child: const Center(child: VPBankLoading()),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  /*-------- Xử lý chụp ảnh -------- */
  Future<void> captureImage() async {
    // bloc.sinkSafe(AccountCameraOcrBlocState.loading);
    // bloc.sinkLoading(true);
    await initiallizeControllerFuture;
    controller.setFlashMode(FlashMode.off);
    // _captureAndCropImage();
    final imageXfile = await controller.takePicture();
    bloc.saveImageIDCard(
      nameFileSave: widget.nameFileSave ?? 'IMAGE_CAPTURE',
      pathXFile: imageXfile.path,
    );
  }

  Future<void> showAcceptImageBottomSheet(BuildContext context) async {
    await showModalBottomSheet(
      context: context,
      isDismissible: false,
      isScrollControlled: false,
      builder: (BuildContext context) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.25,
          // Giới hạn chiều cao tối đa là 80% chiều cao màn hình
          child: Scaffold(
            body: Container(
              height: 200,
              constraints: BoxConstraints(
                maxHeight:
                    MediaQuery.of(context).size.height *
                    0.3, // Giới hạn chiều cao tối đa là 80% chiều cao màn hình
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Bạn có muốn dùng ảnh này?',
                    style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Hãy đảm bảo ảnh đã chụp hiển thị thông tin rõ ràng, không bị mất góc.',
                    style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
                  ),
                  SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: ButtonWidget(
                          colorEnable: themeData.buttonEnableBg,
                          colorBorder:
                              context.isDark
                                  ? Colors.transparent
                                  : themeData.gray700,
                          textStyle: vpTextStyle.subtitle14.copyColor(
                            themeData.gray900,
                          ),
                          action: 'Chụp lại',
                          onPressed: () {
                            context.pop();
                            setState(() => havePathImage = false);
                          },
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: ButtonWidget(
                          action: 'Có, dùng ảnh này',
                          onPressed: () {
                            context.pop();
                            if (widget.onCallBackUse != null) {
                              widget.onCallBackUse?.call(pathSave);
                            } else {
                              context.pop(pathSave);
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
