import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:vp_common/extensions/context_extensions.dart';
import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_settings/common/helper/string_extension.dart';
import 'package:vp_settings/cubit/customer_info/update_contact_info/email/update_email_info_bloc.dart';
import 'package:vp_settings/cubit/customer_info/update_contact_info/phone/update_phone_info_bloc.dart';
import 'package:vp_settings/model/customer_info/customer_info_responses_model.dart';
import 'package:vp_settings/router/setting_router.dart';
import 'package:vp_settings/screen/customer_info/general_info/scan_nfc_page.dart';
import 'package:vp_settings/vp_settings_module.dart';

import '../../../gen/assets.gen.dart';
import 'widget/contact_widget.dart';

class UpdateContactInfoPage extends StatelessWidget {
  final UpdateContactInfoPageParams params;

  const UpdateContactInfoPage({super.key, required this.params});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.hideKeyboard(),
      child: VPScaffold(
        appBar: VPAppBar.layer(title: 'Cập nhật thông tin liên hệ'),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: Scrollbar(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: ListView(
                      children: [
                        const SizedBox(height: 12.0),
                        ContactItem(
                          onTap:
                              () =>
                                  params.info.email != null
                                      ? _showBottomSheetConfirm(
                                        (yesOrNo) => handleNavigation(
                                          context,
                                          yesOrNo,
                                          true,
                                        ),
                                        context,
                                        'email',
                                        params.info.email ?? '',
                                      )
                                      : navigatateCaseEmailNull(),
                          icon: SvgPicture.asset(
                            Assets.icons.contactInfoEmail,
                            colorFilter: ColorFilter.mode(
                              themeData.black,
                              BlendMode.srcIn,
                            ),
                            package: vpSettings,
                          ),
                          text: 'Email',
                        ),
                        const SizedBox(height: 8.0),
                        ContactItem(
                          onTap:
                              () => _showBottomSheetConfirm(
                                (yesOrNo) =>
                                    handleNavigation(context, yesOrNo, false),
                                context,
                                'số điện thoại',
                                params.info.mobile ?? '',
                              ),
                          icon: SvgPicture.asset(
                            Assets.icons.contactInfoPhone,
                            colorFilter: ColorFilter.mode(
                              themeData.black,
                              BlendMode.srcIn,
                            ),
                            package: vpSettings,
                          ),
                          text: 'Số điện thoại',
                        ),
                        const SizedBox(height: 8.0),
                        ContactItem(
                          onTap: () {},
                          icon: SvgPicture.asset(
                            Assets.icons.contactInfoLocation,
                            colorFilter: ColorFilter.mode(
                              themeData.black.withValues(alpha: 0.5),
                              BlendMode.srcIn,
                            ),
                            package: vpSettings,
                          ),
                          text: 'Địa chỉ liên hệ',
                          isDisabled: true,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showBottomSheetConfirm(
    Function(bool yesOrNo) callBack,
    BuildContext context,
    String? content,
    String detail,
  ) {
    VPPopup(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Thay đổi $content',
            style: vpTextStyle.headineBold6.copyColor(themeData.gray900),
          ),
          const SizedBox(height: 4.0),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: 'Bạn còn sử dụng $content ',
                  style: vpTextStyle.body16.copyColor(themeData.gray700),
                ),
                TextSpan(
                  text: '$detail ',
                  style: vpTextStyle.subtitle16.copyColor(themeData.gray900),
                ),
                TextSpan(
                  text: 'không?',
                  style: vpTextStyle.body16.copyColor(themeData.gray700),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12.0),
            child: OptionButton(
              text: 'Có',
              onTap: () {
                context.pop();
                callBack.call(true);
              },
            ),
          ),
          OptionButton(
            text: 'Không',
            onTap: () {
              context.pop();
              callBack.call(false);
            },
          ),
        ],
      ),
      padding: EdgeInsets.all(20),
      contentPadding: EdgeInsets.only(top: 20),
    ).icHandle.showSheet(context);
  }

  void gotoNFC() {
    var data = ScanNfcRequest(
      id: params.info.idNumber ?? '',
      dob: (params.info.dob ?? '').convertDateFormat(),
      doe: (params.info.idExpired ?? '').convertDateFormat(),
      doi: (params.info.idDate ?? '').convertDateFormat(),
      idDate: params.info.idDate ?? '',
    );

    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;

    if (context == null) {
      return;
    }
    context.push(SettingRouter.scanNfcPage.routeName, extra: data);
    return;
  }

  void handleNavigation(BuildContext context, bool yesOrNo, bool isEmail) {
    if (!yesOrNo) {
      if (params.info.isShowNFC) {
        gotoNFC();
        return;
      }

      if (params.info.gotoNFCFromUpdateInfo) {
        context.push(SettingRouter.identityAuthenticationPage.routeName);
        return;
      }
    }

    if (isEmail) {
      context.push(
        SettingRouter.updateEmailPage.routeName,
        extra: UpdateEmailParams(
          params.info.email ?? '',
          yesOrNo,
          params.info.email != null,
        ),
      );
    } else {
      context.push(
        SettingRouter.updatePhoneNumberPage.routeName,
        extra: UpdatePhoneParams(params.info.mobile ?? '', yesOrNo),
      );
    }
  }

  void navigatateCaseEmailNull() {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;

    if (context == null) {
      return;
    }
    if (!params.info.getUserHasNFC) {
      /// identityAuthenticationPage = xac thuc danh tinh
      context.push(SettingRouter.identityAuthenticationPage.routeName);
      return;
    } else {
      /// vao luong input email + face matching
      context.push(
        SettingRouter.updateEmailPage.routeName,
        extra: UpdateEmailParams(
          params.info.email ?? '',
          false,
          params.info.email != null,
        ),
      );
      return;
    }
  }
}

class OptionButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;

  const OptionButton({required this.text, required this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        decoration: BoxDecoration(
          color: themeData.gray100,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: themeData.divider),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              text,
              style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
            ),
            Icon(Icons.arrow_forward_ios, color: themeData.gray900, size: 16),
          ],
        ),
      ),
    );
  }
}

class UpdateContactInfoPageParams {
  final CustomerInfoResponsesModel info;

  UpdateContactInfoPageParams({required this.info});
}
