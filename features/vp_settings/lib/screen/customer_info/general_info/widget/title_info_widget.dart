import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_settings/generated/l10n.dart';
import 'package:vp_settings/vp_settings_module.dart';

import '../../../../gen/assets.gen.dart' as ic;

class TitleInfoWidget extends StatelessWidget {
  const TitleInfoWidget({
    super.key,
    required this.title,
    this.edit = false,
    this.onPressed,
  });

  final String title;
  final bool? edit;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
            ),
          ),
          Offstage(
            offstage: !(edit ?? false),
            child: Row(
              children: [
                SizedBox(
                  width: 32.0,
                  height: 32.0,
                  child: <PERSON><PERSON><PERSON>utt<PERSON>(
                    onPressed: onPressed,
                    icon: SvgPicture.asset(
                      ic.Assets.icons.edit,
                      width: 11.0,
                      colorFilter: ColorFilter.mode(
                        context.colors.iconBrand,
                        BlendMode.srcIn,
                      ),
                      package: vpSettings,
                    ),
                  ),
                ),
                Text(
                  VPSettingsLocalize.current.settings_update,
                  style: vpTextStyle.subtitle14.copyColor(vpColor.textBrand),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
