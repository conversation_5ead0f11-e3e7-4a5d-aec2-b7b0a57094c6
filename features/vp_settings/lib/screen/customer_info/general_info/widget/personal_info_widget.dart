import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_settings/common/helper/string_extension.dart';
import 'package:vp_settings/generated/l10n.dart';
import 'package:vp_settings/router/setting_router.dart';
import 'package:vp_settings/screen/customer_info/general_info/scan_nfc_page.dart';

import '../../../../../cubit/customer_info/general_info/general_info_bloc.dart';
import 'info_widget.dart';
import 'popup_update_identification.dart';
import 'title_info_widget.dart';

class PersonalInfoWidget extends StatelessWidget {
  const PersonalInfoWidget({super.key});

  static const male = '001';
  static const feMale = '002';

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GeneralInfoBloc, GeneralInfoState>(
      builder: (context, state) {
        final bloc = context.read<GeneralInfoBloc>();

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            GestureDetector(
              onTap: () {
                if ((bloc.info!.isOrganizationalCustomer ||
                    bloc.info!.iForeignCustomer)) {
                  return;
                }
                if (bloc.info?.isShowNFC ?? false) {
                  var data = ScanNfcRequest(
                    id: bloc.info?.idNumber ?? '',
                    dob: (bloc.info?.dob ?? '').convertDateFormat(),
                    doe: (bloc.info?.idExpired ?? '').convertDateFormat(),
                    doi: (bloc.info?.idDate ?? '').convertDateFormat(),
                    idDate: bloc.info?.idDate ?? '',
                  );
                  context.push(
                    SettingRouter.scanNfcPage.routeName,
                    extra: data,
                  );
                  return;
                }
                if (bloc.info?.isShowPopupUpdate ?? false) {
                  popupUpdateIdentification(context);
                  return;
                }
                context.push(
                  SettingRouter.identityAuthenticationPage.routeName,
                );
              },
              child: TitleInfoWidget(
                title: VPSettingsLocalize.current.settings_personalInfo,
                edit:
                    !((bloc.info?.isOrganizationalCustomer ?? false) ||
                        (bloc.info?.iForeignCustomer ?? false)),
              ),
            ),
            InfoWidget(
              desc: VPSettingsLocalize.current.settings_fullName,
              info: bloc.info?.fullName,
            ),
            InfoWidget(
              desc: VPSettingsLocalize.current.settings_birthday,
              info: bloc.info?.dob,
            ),
            InfoWidget(
              desc: VPSettingsLocalize.current.settings_sex,
              info: bloc.info?.gender == feMale ? "Nữ" : "Nam",
            ),
            InfoWidget(
              desc: VPSettingsLocalize.current.settings_cmnd,
              info: bloc.info?.idNumber,
            ),
            InfoWidget(
              desc: VPSettingsLocalize.current.settings_dateRange,
              info: bloc.info?.idDate,
            ),
            InfoWidget(
              desc: VPSettingsLocalize.current.settings_placeOfGrant,
              info: bloc.info?.idPlace,
            ),
          ],
        );
      },
    );
  }
}
