import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_settings/generated/l10n.dart';
import 'package:vp_settings/router/setting_router.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/update_contact_info_page.dart';

import '../../../../../cubit/customer_info/general_info/general_info_bloc.dart';
import 'info_widget.dart';
import 'title_info_widget.dart';

class ContactInfoWidget extends StatefulWidget {
  const ContactInfoWidget({super.key});

  @override
  State<ContactInfoWidget> createState() => _ContactInfoWidgetState();
}

class _ContactInfoWidgetState extends State<ContactInfoWidget> {
  late GeneralInfoBloc cubit = context.read<GeneralInfoBloc>();

  String? get email => cubit.info?.email;

  String? get phone => cubit.info?.mobile;

  String? get mobileTransaction => cubit.info?.mobileTransaction;

  String? get address => cubit.info?.address;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GeneralInfoBloc, GeneralInfoState>(
      builder:
          (context, state) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  if ((cubit.info!.isOrganizationalCustomer ||
                      cubit.info!.iForeignCustomer)) {
                    return;
                  }
                  context.push(
                    SettingRouter.settingUpdateContactInfo.routeName,
                    extra: UpdateContactInfoPageParams(info: cubit.info!),
                  );
                },
                child: TitleInfoWidget(
                  title: VPSettingsLocalize.current.settings_contactInfo,
                  edit:
                      !((cubit.info?.isOrganizationalCustomer ?? false) ||
                          (cubit.info?.iForeignCustomer ?? false)),
                ),
              ),

              /// email
              InfoWidget(
                desc: VPSettingsLocalize.current.settings_email,
                info: email,
                icon:
                    email != null && !cubit.isEmailVerify
                        ? Icon(
                          CupertinoIcons.exclamationmark_circle_fill,
                          color: themeData.red,
                          size: 20,
                        )
                        : null,
              ),

              buildVerifyEmailView(),

              /// phone
              InfoWidget(
                desc: VPSettingsLocalize.current.settings_phoneNumber,
                info: phone,
              ),

              /// mobile transaction
              if (mobileTransaction.hasData)
                InfoWidget(
                  desc:
                      VPSettingsLocalize
                          .current
                          .settings_transactionInformation,
                  info: mobileTransaction,
                ),

              /// address
              InfoWidget(
                desc: VPSettingsLocalize.current.settings_address,
                info: address,
              ),
            ],
          ),
    );
  }

  Widget buildVerifyEmailView() {
    if (email.isNullOrEmpty || cubit.emailValid) {
      return const SizedBox(height: 0);
    }

    final style = vpTextStyle.body14.copyColor(themeData.black);

    return Container(
      alignment: Alignment.center,
      width: double.infinity,
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.only(top: 4, bottom: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: themeData.red16,
      ),
      child: Text.rich(
        TextSpan(
          style: style,
          children: [
            TextSpan(
              text: '${VPSettingsLocalize.current.settings_emailNotVerify} ',
            ),
            TextSpan(
              text: VPSettingsLocalize.current.settings_emailverify,
              style: vpTextStyle.body14.copyColor(themeData.red),
              recognizer:
                  TapGestureRecognizer()
                    ..onTap =
                        () => context.push(
                          SettingRouter.verifyOtpContactEmail.routeName,
                          extra: email,
                        ),
            ),
          ],
        ),
      ),
    );
  }
}
