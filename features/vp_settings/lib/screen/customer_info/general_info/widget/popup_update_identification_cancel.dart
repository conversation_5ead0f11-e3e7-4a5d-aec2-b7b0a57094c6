import 'package:flutter/material.dart';
import 'package:vp_core/utils/go_router_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_settings/common/enum/navigate_nfc_type.dart';
import 'package:vp_settings/router/setting_router.dart';

Future<void> popupUpdateIdentificationCancel(
  BuildContext context, {
  NavigateNFCType type = NavigateNFCType.setting,
}) async {
  VPPopup.outlineAndPrimaryButton(
        title: 'Huỷ thay đổi thông tin?',
        content:
            '<PERSON>u<PERSON> khách có chắc chắn muốn dừng việc thay đổi thông tin? Hệ thống sẽ không lưu dữ liệu đang thực hiện khi Quý khách xác nhận huỷ',
      )
      .copyWith(
        button: VpsButton.secondarySmall(title: "Đóng", onPressed: context.pop),
      )
      .copyWith(
        button: VpsButton.primarySmall(
          title: "C<PERSON>, huỷ",
          onPressed: () {
            switch (type) {
              case NavigateNFCType.setting:
                context.popUntilRoute(SettingRouter.generalInfo.routeName);
                break;
              case NavigateNFCType.bank:
                context.popUntilRoute(SettingRouter.openPartnerList.routeName);
                break;
            }
          },
        ),
      )
      .showDialog(context);
}
