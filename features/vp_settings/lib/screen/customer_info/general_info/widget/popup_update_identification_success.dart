import 'package:flutter/material.dart';
import 'package:vp_common/generated/assets.gen.dart';
import 'package:vp_core/utils/go_router_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_settings/common/enum/navigate_nfc_type.dart';
import 'package:vp_settings/router/setting_router.dart';

Future<void> popupUpdateIdentificationSuccess(
  BuildContext context, {
  NavigateNFCType type = NavigateNFCType.setting,
}) async {
  VPPopup.oneButton(title: '', content: 'Cập nhật thành công')
      .copyWith(
        icon: Assets.icons.icSuccessNew.svg(),
        button: VpsButton.primarySmall(
          title: "Đã hiểu",
          onPressed: () {
            switch (type) {
              case NavigateNFCType.setting:
                context.popUntilRoute(SettingRouter.customerInfo.routeName);
                context.replace(SettingRouter.customerInfo.routeName);
                break;
              case NavigateNFCType.bank:
                context.goNamed(
                  SettingRouter.openPartnerList.routeName,
                  extra: {'showPopupConfirmOpenBackAccount': true},
                );
                break;
            }
          },
        ),
      )
      .showDialog(context);
}
