import 'package:flutter/material.dart';
import 'package:vp_core/utils/go_router_helper.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_settings/router/setting_router.dart';

Future<void> popupUpdateIdentificationError(
  BuildContext context,
  String? messageError,
) async {
  VPPopup.oneButton(
        title: 'Cập nhật thất bại',
        content:
            'Có lỗi xảy ra. Vui lòng quay lại màn thông tin khách hàng và thử lại sau.',
      )
      .copyWith(
        button: VpsButton.primarySmall(
          title: "Đã hiểu",
          onPressed: () {
            context.popUntilRoute(SettingRouter.customerInfo.routeName);
          },
        ),
      )
      .showDialog(context);
}
