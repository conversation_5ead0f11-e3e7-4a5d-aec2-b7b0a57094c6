import 'dart:io';

import 'package:flutter/material.dart';
import 'package:nfc_plugin_flutter/nfc_plugin_flutter.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/app_device_id.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/gen/assets.gen.dart' as vp_design_system;
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_settings/common/enum/navigate_nfc_type.dart';
import 'package:vp_settings/core/repository/customer_info_repository.dart';
import 'package:vp_settings/model/customer_info/nfc/read_nfc_request.dart';
import 'package:vp_settings/model/customer_info/nfc/save_log_nfc_request.dart';
import 'package:vp_settings/router/setting_router.dart';
import 'package:vp_settings/vp_settings_module.dart';
import '../../../gen/assets.gen.dart' as asset;
import 'widget/loading_utils.dart';
import 'widget/popup_error.dart';
import 'widget/popup_update_identification_cancel.dart';

class ScanNfcRequest {
  final String id;
  final String dob;
  final String doe;
  final String doi;
  final String idDate;
  final String? idTypeNew;
  final NavigateNFCType? nfcType;

  const ScanNfcRequest({
    required this.id,
    required this.dob,
    required this.doe,
    required this.doi,
    required this.idDate,
    this.idTypeNew,
    this.nfcType,
  });
}

class ScanNfcPage extends StatefulWidget {
  final ScanNfcRequest scanNfcRequest;

  const ScanNfcPage({super.key, required this.scanNfcRequest});

  @override
  State<ScanNfcPage> createState() => _ScanNfcPageState();
}

class _ScanNfcPageState extends State<ScanNfcPage> {
  late ScanNfcRequest _scanNfcData;

  ScanNfcRequest get _scanNfcRequest => widget.scanNfcRequest;

  @override
  void initState() {
    super.initState();
    _scanNfcData = _scanNfcRequest;
  }

  @override
  Widget build(BuildContext context) {
    return VPScaffold(
      appBar: VPAppBar.flows(
        title: 'Quét thông tin chip CCCD',
        leading: IconButton(
          onPressed: () {
            popupUpdateIdentificationCancel(
              context,
              type: _scanNfcRequest.nfcType ?? NavigateNFCType.setting,
            );
          },
          icon: vp_design_system.Assets.icons.appbar.icClose.svg(
            colorFilter: ColorFilter.mode(vpColor.iconPrimary, BlendMode.srcIn),
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(height: 16),
                      Text(
                        'Đặt khu vực chip của thẻ CCCD lên mặt sau phía trên điện thoại để bắt đầu quét. Giữ thẻ đúng vị trí đến khi thấy dấu tích báo thành công',
                        style: vpTextStyle.body16.copyColor(themeData.gray900),
                      ),
                      SizedBox(height: 16),
                      Image.asset(
                        asset.Assets.images.imageScanNfc.keyName,
                        package: vpSettings,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(
                width: 1,
                color: vpColor.backgroundElevationMinus1,
              ),
            ),
          ),
          child: GestureDetector(
            onTap: () => _goToNFC(_scanNfcData, context),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: Container(
                    height: 40,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      color: const Color(0xFF008757),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      "Bắt đầu",
                      textAlign: TextAlign.center,
                      style: vpTextStyle.subtitle14.copyColor(
                        themeData.textEnable,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _goToNFC(ScanNfcRequest data, BuildContext context) async {
    dynamic res = await SampleCallNativeFlutter.nfcScan(
      data.id,
      data.dob,
      data.doe,
      data.doi,
      'vi',
    );
    final iamUserInfo = GetIt.instance<AuthCubit>().customerInfoIam;
    SaveLogNFCRequest logNFCRequest = SaveLogNFCRequest(
      mobile: iamUserInfo?.mobile,
      accountNo: iamUserInfo?.accountNo,
      deviceName: await AppUtils.getDeviceName(),
      status: "FAIL",
    );
    if (res['error'] != null) {
      switch (res['error']) {
        case "WRONG_CITIZEN_ID_CARD":
        case "InvalidMRZKey":
          await context.push(SettingRouter.inputInfoNfcPage.routeName).then((
            dynamic value,
          ) {
            if (value == null) return;
            _scanNfcData = value;
          });
          logNFCRequest.message = 'WRONG_CITIZEN_ID_CARD/InvalidMRZKey';
          await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
            request: logNFCRequest,
          );
          return;
        case "ERROR_CODE_UN_SUPPORT_NFC":
        case "NFCNotSupported":
          String message = 'Thiết bị không hỗ trợ đọc NFC';
          popupError(
            context,
            messageError: message,
            type: _scanNfcRequest.nfcType,
          );
          logNFCRequest.message = message;
          await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
            request: logNFCRequest,
          );
          return;
        case "CANNOT_OPEN_DEVICE":
          String message =
              'Thiết bị có hỗ trợ NFC nhưng không thể kết nối đến chức năng NFC của hệ thống';
          popupError(
            context,
            messageError: message,
            type: _scanNfcRequest.nfcType,
          );
          logNFCRequest.message = message;
          await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
            request: logNFCRequest,
          );
          return;
        case "NFC_IS_OFF":
          String message = 'Chức năng NFC bị tắt';
          popupError(
            context,
            messageError: message,
            type: _scanNfcRequest.nfcType,
          );
          logNFCRequest.message = message;
          await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
            request: logNFCRequest,
          );
          return;
        case "ERROR_CODE_UN_SUPPORT_API_VERSION":
          String message = 'Lỗi hệ điều hành không được hỗ trợ';
          popupError(
            context,
            messageError: message,
            type: _scanNfcRequest.nfcType,
          );
          logNFCRequest.message = message;
          await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
            request: logNFCRequest,
          );
          return;
        case "ERROR_TIME_OUT":
        case "Timeout":
          String message = 'Hết thời gian chờ';
          popupError(
            context,
            messageError: message,
            onBack: () => context.pop(),
          );
          logNFCRequest.message = message;
          await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
            request: logNFCRequest,
          );
          return;
        case "CARD_LOST_CONNECTION":
        case "ConnectionError":
          String message =
              'Thẻ bị đưa ra ngoài vùng đọc của NFC hoặc mất kết nối đến thẻ (lỗi hệ thống)';
          popupError(
            context,
            messageError: message,
            onBack: () => context.pop(),
          );
          logNFCRequest.message = message;
          await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
            request: logNFCRequest,
          );
          return;
        case "UNKNOWN":
        case "ResponseError":
          String message = 'Lỗi không xác định';
          popupError(
            context,
            messageError: message,
            onBack: () => context.pop(),
          );
          logNFCRequest.message = message;
          await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
            request: logNFCRequest,
          );
          return;
        case "Tag response error / no response":
        case "Tag is not connected":
        case "Tag connection lost":
        case "Session invalidated":
          String message = 'Vui lòng giữ yên vị trí thẻ và thử lại';
          popupError(
            context,
            messageError: message,
            onBack: () => context.pop(),
          );
          logNFCRequest.message = message;
          await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
            request: logNFCRequest,
          );
          return;
        case "UnexpectedError":
          String message =
              'Lỗi xảy ra khi không ngắt 2 phiên đọc NFC cách nhau 1.5s';
          popupError(
            context,
            messageError: message,
            onBack: () => context.pop(),
          );
          logNFCRequest.message = message;
          await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
            request: logNFCRequest,
          );
          return;
        case "ERROR_CODE_USER_CANCELED":
        case "UserCanceled":
          return;
        default:
          popupError(
            context,
            messageError: res['error'],
            type: _scanNfcRequest.nfcType,
          );
          logNFCRequest.message = res['error'];
          await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
            request: logNFCRequest,
          );
          return;
      }
    }
    logNFCRequest.status = 'SUSSCESS';
    await GetIt.instance<CustomerInfoRepository>().saveNFCLog(
      request: logNFCRequest,
    );
    LoadingUtil.showLoading();
    ReadNFCRequest nfcRequest =
        Platform.isAndroid
            ? ReadNFCRequest(
              aAResult: res['aAResult'],
              challenge: res['challenge'],
              dg1: res['dg1'],
              dg2: res['dg2'],
              dg3: res['dg3'],
              dg4: res['dg4'],
              dg5: res['dg5'],
              dg6: res['dg6'],
              dg7: res['dg7'],
              dg8: res['dg8'],
              dg9: res['dg9'],
              dg10: res['dg10'],
              dg11: res['dg11'],
              dg12: res['dg12'],
              dg13: res['dg13'],
              dg14: res['dg14'],
              dg15: res['dg15'],
              dg16: res['dg16'],
              eACCAResult: res['eACCAResult'],
              sod: res['sod'],
              idDate: data.idDate,
              idTypeNew: data.idTypeNew,
            )
            : ReadNFCRequest(
              aAResult: res['data']['aAResult'],
              challenge: res['data']['challenge'],
              dg1: res['data']['dg1'],
              dg2: res['data']['dg2'],
              dg3: res['data']['dg3'],
              dg4: res['data']['dg4'],
              dg5: res['data']['dg5'],
              dg6: res['data']['dg6'],
              dg7: res['data']['dg7'],
              dg8: res['data']['dg8'],
              dg9: res['data']['dg9'],
              dg10: res['data']['dg10'],
              dg11: res['data']['dg11'],
              dg12: res['data']['dg12'],
              dg13: res['data']['dg13'],
              dg14: res['data']['dg14'],
              dg15: res['data']['dg15'],
              dg16: res['data']['dg16'],
              eACCAResult: res['data']['eACCAResult'],
              sod: res['data']['sod'],
              idDate: data.idDate,
              idTypeNew: data.idTypeNew,
            );
    try {
      final result = await GetIt.instance<CustomerInfoRepository>()
          .readNFCExternal(nfcRequest: nfcRequest);
      final resReadNFC = result.data;
      LoadingUtil.hideLoading();
      resReadNFC?.nfcType = _scanNfcRequest.nfcType;
      context.push(
        SettingRouter.acceptInfoNfcPage.routeName,
        extra: resReadNFC,
      );
    } catch (e) {
      LoadingUtil.hideLoading();
      final message = await getErrorMessage(e);
      popupError(context, messageError: message, type: _scanNfcRequest.nfcType);
    }
  }
}
