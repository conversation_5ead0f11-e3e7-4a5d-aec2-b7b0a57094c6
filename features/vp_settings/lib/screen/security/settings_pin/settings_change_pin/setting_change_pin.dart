import 'package:flutter/material.dart';
import 'package:vp_common/extensions/context_extensions.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_settings/cubit/security/settings_pin/settings_change_pin/setting_change_pin_cubit.dart';
import 'package:vp_settings/generated/l10n.dart';
import 'package:vp_settings/model/change_pin/change_pin_callback_obj.dart';
import 'package:vp_settings/screen/security/settings_pin/settings_pin_helper/change_pin_view/change_pin_view.dart';

class SettingChangePin extends StatefulWidget {
  const SettingChangePin({super.key});

  @override
  State<SettingChangePin> createState() => _SettingChangePinState();
}

class _SettingChangePinState extends State<SettingChangePin> {
  final SettingChangePinBloc _bloc = SettingChangePinBloc();

  ValueNotifier<bool> notifierEnableBtn = ValueNotifier(false);

  ChangePinCallBackObj? changePinCallBackObj;

  @override
  void dispose() {
    notifierEnableBtn.dispose();
    _bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return VPScaffold(
      appBar: VPAppBar.layer(
        title: VPSettingsLocalize.current.settings_pinChangePin,
      ),
      body: LoadingBuilders<SettingChangePinBloc>(
        bloc: _bloc,
        child: SafeArea(
          child: GestureDetector(
            onTap: () {
              context.hideKeyboard();
            },
            child: Column(
              children: [
                const SizedBox(height: 8.0),
                Expanded(
                  child: ChangePinView(
                    onPinCallBackObj: (obj) {
                      notifierEnableBtn.value = obj.validAll;
                      changePinCallBackObj = obj;
                    },
                  ),
                ),
                DividerWidget(),
                buildActionButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildActionButton() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      width: double.infinity,
      child: ValueListenableBuilder<bool>(
        valueListenable: notifierEnableBtn,
        builder: (context, enable, _) {
          return VpsButton.primarySmall(
            onPressed: () {
              changePin();
            },
            title: VPSettingsLocalize.current.settings_pinBtnChangePin,
            disabled: !enable,
          );
        },
      ),
    );
  }

  void changePin() async {
    context.hideKeyboard();

    final response = await _bloc.changePin(
      currentPin: changePinCallBackObj?.currentPin ?? '',
      newPin: changePinCallBackObj?.newPin ?? '',
    );

    final isSuccess = response.$1 ?? false;

    final message = response.$2;

    if (isSuccess) showDialogChangePinSuccess(message);
  }

  void showDialogChangePinSuccess(String? content) {
    VPPopup.oneButton(
          title: VPSettingsLocalize.current.settings_pinChangeSuccess,
          content: content ?? VPSettingsLocalize.current.settings_pinChangeNotiSuccess,
        )
        .copyWith(
          button: VpsButton.primarySmall(
            title: VPSettingsLocalize.current.settings_close,
            onPressed: context.pop,
          ),
        )
        .showDialog(context)
        .then((_) => context.pop());
  }
}
