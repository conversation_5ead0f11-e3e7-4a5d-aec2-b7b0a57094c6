import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_settings/core/constant/validate.dart';
import 'package:vp_settings/cubit/security/settings_change_pass/change_pass_view_bloc.dart';
import 'package:vp_settings/cubit/security/settings_change_pass/setting_change_pass_bloc.dart';
import 'package:vp_settings/generated/l10n.dart';
import 'package:vp_settings/model/change_pass/change_pass_callback_obj.dart';

import 'setting_pass_validate_view.dart';

class ChangePassView extends StatefulWidget {
  const ChangePassView({super.key, required this.onPassCallBackObj});

  final Function(ChangePassCallBackObj) onPassCallBackObj;

  @override
  State<ChangePassView> createState() => ChangePassViewState();
}

class ChangePassViewState extends State<ChangePassView> {
  ChangePassViewCubit bloc = ChangePassViewCubit();

  @override
  void dispose() {
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const maxLength = AccountValidate.passMaxLength;
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              VPSettingsLocalize.current.settings_enterYourPassword,
              style: vpTextStyle.subtitle16.copyColor(themeData.black),
              textAlign: TextAlign.start,
            ),
            const SizedBox(height: 16.0),
            builTextOldPass(),
            const SizedBox(height: 10.0),
            buildTextFieldPass(
              VPSettingsLocalize.current.settings_newPassword,
              bloc.controlerStatePass.stream,
              bloc.txtPass,
              () {
                if (bloc.txtPass.text.length < maxLength) {
                  bloc.controlerStatePass.sink.add(SettingBaseStateUI.normal);
                }
                bloc.validPass(context);
                callBackChangePass();
              },
            ),
            const SizedBox(height: 10.0),
            buildTextFieldPass(
              VPSettingsLocalize.current.settings_confirmNewPassword,
              bloc.controlerStateRePass.stream,
              bloc.txtRePass,
              () {
                if (bloc.txtRePass.text.length < maxLength) {
                  bloc.controlerStateRePass.sink.add(SettingBaseStateUI.normal);
                }
                bloc.validRePass(context);
                callBackChangePass();
              },
            ),
            SettingsPassValidateView(controller: bloc.txtPass),
            const SizedBox(height: 16.0),
          ],
        ),
      ),
    );
  }

  /*------ Nhap mat khau cu ------ */
  Widget builTextOldPass() {
    return VPTextField.small(
      maxLength: AccountValidate.passMaxLength,
      hintText: VPSettingsLocalize.current.settings_pInputOldPass,
      obscureText: true,
      keyboardType: TextInputType.text,
      controller: bloc.txtOldPass,
      onChanged: (value) {
        callBackChangePass();
      },
    );
  }

  /*------ Nhap mat khau moi va xac nhan mat khau ------ */
  StreamBuilder buildTextFieldPass(
    String hintText,
    Stream stream,
    TextEditingController controller,
    Function() onchanged,
  ) {
    return StreamBuilder(
      stream: stream,
      builder: (context, snapshot) {
        String? message;
        if (snapshot.error is String) {
          message = snapshot.error as String;
        }
        final isSuccess = snapshot.hasData && (snapshot.data is String);
        if (snapshot.data is String) {
          message = snapshot.data as String;
        }
        const maxLength = AccountValidate.passMaxLength;
        return VPTextField.small(
          maxLength: maxLength,
          obscureText: true,
          autocorrect: true,
          keyboardType: TextInputType.text,
          controller: controller,
          hintText: hintText,
          captionText: message,
          inputType: (message ?? '').isEmpty ? InputType.rest : InputType.error,
          onChanged: (value) {
            onchanged();
          },
        );
      },
    );
  }

  void callBackChangePass() {
    widget.onPassCallBackObj(
      ChangePassCallBackObj(
        currentPass: bloc.txtOldPass.text,
        newPass: bloc.txtPass.text,
        valid: bloc.valid(),
      ),
    );
  }
}
