import 'package:vp_core/vp_core.dart';
import 'package:vp_settings/common/enum/navigate_nfc_type.dart';
import 'package:vp_settings/cubit/customer_info/nickname/nickname_cubit.dart';
import 'package:vp_settings/cubit/customer_info/nickname/nickname_validate_cubit.dart';
import 'package:vp_settings/cubit/customer_info/update_contact_info/email/update_email_info_bloc.dart';
import 'package:vp_settings/cubit/customer_info/update_contact_info/phone/update_phone_info_bloc.dart';
import 'package:vp_settings/model/customer_info/customer_info_responses_model.dart';
import 'package:vp_settings/model/customer_info/nfc_entity.dart';
import 'package:vp_settings/screen/customer_info/add_bank_acc/add_bank_acc_page.dart';
import 'package:vp_settings/screen/customer_info/customer_info_page.dart';
import 'package:vp_settings/screen/customer_info/general_info/accept_info_nfc_page.dart';
import 'package:vp_settings/screen/customer_info/general_info/general_info_page.dart';
import 'package:vp_settings/screen/customer_info/general_info/identity_authentication_page.dart';
import 'package:vp_settings/screen/customer_info/general_info/input_info_nfc_page.dart';
import 'package:vp_settings/screen/customer_info/general_info/scan_nfc_page.dart';
import 'package:vp_settings/screen/customer_info/general_info/widget/verify_email_success_page.dart';
import 'package:vp_settings/screen/customer_info/nickname/nickname_page.dart';
import 'package:vp_settings/screen/customer_info/signature/detail_signature_page.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/faceliveness_introview_page.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/input_email_screen.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/input_phone_screen.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/otp/email/email_otp_page.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/otp/email/email_otp_step2_page.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/otp/phone/verify_otp_page.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/otp/phone/verify_otp_step2_page.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/update_contact_info_page.dart';
import 'package:vp_settings/screen/security/device_security/device_security_page.dart';
import 'package:vp_settings/screen/security/setting_security_main.dart';
import 'package:vp_settings/screen/security/settings_change_pass/setting_change_pass.dart';
import 'package:vp_settings/screen/security/settings_pin/settings_change_pin/setting_change_pin.dart';
import 'package:vp_settings/screen/security/settings_pin/settings_forgot_pin/settings_forgot_pin.dart';
import 'package:vp_settings/screen/setup/settings_setup_page.dart';
import 'package:vp_settings/screen/setup/setup_color_and_language/setup_color_and_language_page.dart';
import 'package:vp_settings/screen/setup/setup_configuration_account/setup_configuration_account_page.dart';
import 'package:vp_settings/screen/setup/setup_configuration_transaction/setup_configuration_transaction_page.dart';
import 'package:vp_settings/screen/setup/setup_notice_app/setup_notice_app_page.dart';
import 'package:vp_settings/screen/setup/setup_notice_email/setup_notice_email_page.dart';
import 'package:vp_settings/screen/setup/setup_notice_sms/setup_notice_sms_page.dart';
import 'package:vp_settings/screen/support/settings_support_page.dart';
import 'package:vp_settings/screen/support/support_contact_us/support_contact_us_page.dart';
import 'package:vp_settings/screen/support/support_feedback/support_feedback_page.dart';
import 'package:vp_settings/screen/support/support_follow_us/support_follow_us_page.dart';

enum SettingRouter {
  customerInfo('/customerInfo'),
  generalInfo('/generalInfo'),
  nickname('/nickname'),
  settingSecurity('/settingSecurity'),
  settingSetup('/settingSetup'),
  settingSupport('/settingSupport'),
  settingChangePass('/changePass'),
  settingChangePIN('/changePIN'),
  settingForgotPIN('/forgotPIN'),
  settingRegisterSmartOTP('/settingRegisterSmartOTP'),
  settingRegisterSmartOTPResult('/settingRegisterSmartOTPResult'),
  settingSmartOTPChangePinResult('/settingSmartOTPChangePinResult'),
  settingDeviceSecurity('/settingDeviceSecurity'),
  settingUpdateContactInfo('/settingUpdateContactInfo'),
  setupNoticeApp('/setupNoticeApp'),
  setupNoticeEmail('/setupNoticeEmail'),
  setupNoticeSms('/setupNoticeSms'),
  setupConfigurationAccount('/setupConfigurationAccount'),
  setupConfigurationTransaction('/setupConfigurationTransaction'),
  setupColorAndLanguage('/setupColorAndLanguage'),
  supportContactUs('/supportContactUs'),
  supportFeedback('/supportFeedback'),
  supportFollowUs('/supportFollowUs'),
  addBankAcc('/addBankAcc'),
  registerSmartOTPSplash('/registerSmartOTPSplash'),
  genSmartOTPPage('/genSmartOTPPage'),
  mainSmartOTP('/mainSmartOTP'),
  registerSmartOTPInputPin('/registerSmartOTPInputPin'),
  verifyEmailSuccessPage('/verifyEmailSuccessPage'),
  identityAuthenticationPage('/identityAuthenticationPage'),
  scanNfcPage('/scanNfcPage'),
  acceptInfoNfcPage('/acceptInfoNfcPage'),
  inputInfoNfcPage('/inputInfoNfcPage'),
  updatePhoneNumberPage('/updatePhoneNumberPage'),
  updateEmailPage('/updateEmailPage'),
  faceLivenessUpdateContact('/faceLivenessUpdateContact'),
  verifyOTPPage('/verifyOTPPage'),
  verifyOTPPageStep2('/verifyOTPPageStep2'),
  verifyOTPEmailPage('/verifyOTPEmailPage'),
  verifyOTPEmailPageStep2('/verifyOTPEmailPageStep2'),
  openPartnerList('/openPartnerList'),
  verifyOtpContactEmail('/verifyOtpContactEmail'),
  uploadOcrPage('/uploadOcrPage'),
  signatureDetailPage('/signatureDetailPage');

  final String routeName;

  const SettingRouter(this.routeName);
}

List<GoRoute> listSettingRouter() {
  return [
    // /*----- Thông tin khách hàng -----*/
    GoRoute(
      path: SettingRouter.customerInfo.routeName,
      builder: (context, state) => const CustomerInfoPage(),
    ),
    GoRoute(
      path: SettingRouter.generalInfo.routeName,
      builder: (context, state) => const GeneralInfoPage(),
    ),
    GoRoute(
      path: SettingRouter.nickname.routeName,
      builder:
          (context, state) => MultiBlocProvider(
            providers: [
              BlocProvider(create: (context) => NicknameCubit()),
              BlocProvider(create: (context) => NickNameValidateCubit()),
            ],
            child: NicknamePage(),
          ),
    ),
    GoRoute(
      path: SettingRouter.verifyEmailSuccessPage.routeName,
      builder: (context, state) => const VerifyEmailSuccessPage(),
    ),

    GoRoute(
      path: SettingRouter.settingUpdateContactInfo.routeName,
      builder:
          (context, state) => UpdateContactInfoPage(
            params: state.extra as UpdateContactInfoPageParams,
          ),
    ),
    GoRoute(
      path: SettingRouter.settingSetup.routeName,
      builder: (context, state) => const SettingsSetupPage(),
    ),
    GoRoute(
      path: SettingRouter.settingSupport.routeName,
      builder: (context, state) => const SettingsSupportPage(),
    ),

    /*----- Bảo mật -----*/
    GoRoute(
      path: SettingRouter.settingSecurity.routeName,
      builder: (context, state) => const SettingSecurityMain(),
    ),
    GoRoute(
      path: SettingRouter.settingChangePass.routeName,
      builder: (context, state) => const SettingChangePass(),
    ),
    GoRoute(
      path: SettingRouter.settingChangePIN.routeName,
      builder: (context, state) => SettingChangePin(),
    ),
    GoRoute(
      path: SettingRouter.settingDeviceSecurity.routeName,
      builder: (context, state) => const DeviceSecurityPage(),
    ),
    GoRoute(
      path: SettingRouter.settingForgotPIN.routeName,
      builder: (context, state) => SettingForgotPin(),
    ),
    GoRoute(
      path: SettingRouter.setupNoticeApp.routeName,
      builder: (context, state) => const SetupNoticeAppPage(),
    ),
    GoRoute(
      path: SettingRouter.setupNoticeEmail.routeName,
      builder: (context, state) => const SetupNoticeEmailPage(),
    ),
    GoRoute(
      path: SettingRouter.setupNoticeSms.routeName,
      builder: (context, state) => const SetupNoticeSmsPage(),
    ),
    GoRoute(
      path: SettingRouter.setupConfigurationAccount.routeName,
      builder: (context, state) => const SetupConfigurationAccountPage(),
    ),
    GoRoute(
      path: SettingRouter.setupConfigurationTransaction.routeName,
      builder: (context, state) => const SetupConfigurationTransactionPage(),
    ),
    GoRoute(
      path: SettingRouter.setupColorAndLanguage.routeName,
      builder: (context, state) => const SetupColorAndLanguagePage(),
    ),

    /*----- Setting - Support ------ */
    GoRoute(
      path: SettingRouter.supportContactUs.routeName,
      builder: (context, state) => const SupportContactUsPage(),
    ),
    GoRoute(
      path: SettingRouter.supportFeedback.routeName,
      builder: (context, state) => const SupportFeedbackPage(),
    ),
    GoRoute(
      path: SettingRouter.supportFollowUs.routeName,
      builder: (context, state) => const SupportFollowUsPage(),
    ),
    GoRoute(
      path: SettingRouter.addBankAcc.routeName,
      builder:
          (context, state) =>
              AddBankAccPage(info: state.extra as CustomerInfoResponsesModel),
    ),
    // /*----- NFC Identity Flow -----*/
    GoRoute(
      path: SettingRouter.identityAuthenticationPage.routeName,
      builder:
          (context, state) =>
              IdentityAuthenticationPage(type: state.extra as int?),
    ),
    GoRoute(
      path: SettingRouter.scanNfcPage.routeName,
      builder:
          (context, state) =>
              ScanNfcPage(scanNfcRequest: state.extra as ScanNfcRequest),
    ),
    GoRoute(
      path: SettingRouter.acceptInfoNfcPage.routeName,
      builder:
          (context, state) =>
              AcceptInfoNfcPage(dataInfo: state.extra as NFCEntity?),
    ),
    GoRoute(
      path: SettingRouter.inputInfoNfcPage.routeName,
      builder: (context, state) => const InputInfoNfcPage(),
    ),

    // /*----- Update Info -----*/
    GoRoute(
      path: SettingRouter.updatePhoneNumberPage.routeName,
      builder:
          (context, state) => BlocProvider(
            create:
                (context) => UpdateContactInfoBloc(
                  args: state.extra as UpdatePhoneParams,
                ),
            child: const UpdatePhoneNumberPage(),
          ),
    ),
    GoRoute(
      path: SettingRouter.updateEmailPage.routeName,
      builder:
          (context, state) => BlocProvider(
            create:
                (context) =>
                    UpdateEmailInfoBloc(args: state.extra as UpdateEmailParams),
            child: const UpdateEmailPage(),
          ),
    ),
    GoRoute(
      path: SettingRouter.faceLivenessUpdateContact.routeName,
      builder:
          (context, state) => FaceLivenessIntroviewPage(
            data: state.extra as Map<String, dynamic>,
          ),
    ),

    // /*----- OTP Flow -----*/
    GoRoute(
      path: SettingRouter.verifyOTPPage.routeName,
      builder:
          (context, state) =>
              VerifyOTPPage(bloc: state.extra as UpdateContactInfoBloc),
    ),
    GoRoute(
      path: SettingRouter.verifyOTPPageStep2.routeName,
      builder:
          (context, state) =>
              VerifyOTPStep2Page(bloc: state.extra as UpdateContactInfoBloc),
    ),
    GoRoute(
      path: SettingRouter.verifyOTPEmailPage.routeName,
      builder:
          (context, state) =>
              EmailOTPPage(bloc: state.extra as UpdateEmailInfoBloc),
    ),
    GoRoute(
      path: SettingRouter.verifyOTPEmailPageStep2.routeName,
      builder:
          (context, state) =>
              EmailOTPStep2Page(bloc: state.extra as UpdateEmailInfoBloc),
    ),
    GoRoute(
      path: SettingRouter.signatureDetailPage.routeName,
      builder:
          (context, state) =>
              DetailSignaturePage(imageUrl: state.extra as String),
    ),
  ];
}
