import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_design_system/custom_widget/app_snackbar_utils.dart';
import 'package:vp_settings/core/repository/setting_change_pin_repository.dart';
import 'package:vp_settings/model/change_pin/change_pin_request.dart';

class SettingChangePinBloc with Loading {
  final SettingChangePinRepository _repository =
      GetIt.instance<SettingChangePinRepository>();

  /// bool: isSuccess
  /// String: message from api
  Future<(bool, String?)> changePin({
    required String currentPin,
    required String newPin,
  }) async {
    bool isSuccess = false;
    String? message;
    try {
      await loading(() async {
        final changePinParam = ChangePinRequest(
          currentPin: currentPin,
          newPin: newPin,
        );

        final response = await _repository.changePin(changePinParam);

        if (response?.isSuccess ?? false) {
          // TODO: save pin for current session
          // Session().setPinCode(newPin);
        } else {
          showMessage(response?.message);
        }
        isSuccess = response?.isSuccess ?? false;
        message = response?.message;
      });
    } catch (e, stackTrace) {
      showError(e);
      debugPrintStack(stackTrace: stackTrace);
    }
    return (isSuccess, message);
  }

  void dispose() {
    disposeLoading();
  }
}
