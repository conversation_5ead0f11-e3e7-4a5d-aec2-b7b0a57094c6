import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_settings/core/repository/customer_info_repository.dart';
import 'package:vp_settings/cubit/customer_info/update_contact_info/otp/verify_otp_bloc.dart';
import 'package:vp_settings/cubit/customer_info/update_contact_info/otp/verify_otp_step2_bloc.dart';
import 'package:vp_settings/generated/l10n.dart';
import 'package:vp_settings/router/setting_router.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/utils.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/widget/dialog_confirm.dart';

part 'update_phone_info_state.dart';

class UpdatePhoneParams {
  final String phone;
  final bool stillUseOldPhone;

  UpdatePhoneParams(this.phone, this.stillUseOldPhone);
}

class UpdateContactInfoBloc extends Cubit<UpdateContactInfoState> with Loading {
  UpdateContactInfoBloc({required UpdatePhoneParams args})
    : super(UpdateContactInfoState()) {
    updatePhoneParams = args;
  }

  final customerInfoRepo = GetIt.instance<CustomerInfoRepository>();
  late UpdatePhoneParams updatePhoneParams;
  bool enableContinues = false;
  TextEditingController phoneController = TextEditingController();
  FocusNode phoneFocusNode = FocusNode();

  final phoneKey = GlobalKey<FormState>();
  late String _transId;

  //step0
  Future<bool> generateOTPNewPhone() async {
    bool result = false;
    try {
      await loading(() async {
        Map<String, String> params = {
          'mobile': phoneController.text.trim(),
          'verificationMethod': updatePhoneParams.stillUseOldPhone ? '0' : '2',
        };
        final baseResponse = await customerInfoRepo.userDraftNewPhone(params);
        if (baseResponse.isSuccess) {
          _transId = baseResponse.data['transId'];
          result = true;
        } else {
          showMessage(baseResponse.message);
          result = false;
        }
      });
    } catch (e) {
      if (e is ResponseError) {
        showMessage(e.message, isSuccess: false);
      } else {
        showError(e);
      }
      return false;
    }
    return result;
  }

  //step 1
  void verificationUserDraft(
    VerifyOTPBloc verifyOTPBloc,
    String otp,
    BuildContext context,
    UpdateContactInfoBloc bloc,
  ) async {
    try {
      await loading(() async {
        final response = await customerInfoRepo.verificationUserDraft(
          _transId,
          otp,
        );
        if (response.isSuccess) {
          showDialogConfirm(context, bloc);
        } else {
          throw response;
        }
      });
    } catch (e) {
      if (e is BaseResponse) {
        otpError(verifyOTPBloc, e);
      } else {
        showError(e);
      }
    }
  }

  //step1.1
  void showDialogConfirm(BuildContext context, UpdateContactInfoBloc bloc) {
    ContactUtilsWidget.showDialogConfirm(
      context,
      callBack: () {
        if (updatePhoneParams.stillUseOldPhone) {
          confirmChangeToNewPhone(context, bloc);
        } else {
          confirmChangeWithFaceLiveness();
        }
      },
      conntent: contentDialog(),
      title: 'Xác nhận thay đổi số điện thoại',
    );
  }

  void confirmChangeToNewPhone(
    BuildContext context,
    UpdateContactInfoBloc bloc,
  ) async {
    context.push(SettingRouter.verifyOTPPageStep2.routeName, extra: bloc);
  }

  void initGenOTPStep2(VerifyOTPStep2Bloc verifyOTPStep2Bloc) async {
    emit(state.copyWith(stepPhone: StepOTPPhone.newPhone));

    /// api gen otp step 2
    try {
      await loading(() async {
        await customerInfoRepo.confirmChangePhone(_transId);
      });
    } catch (e) {
      if (e is BaseResponse) {
        otpStep2Error(verifyOTPStep2Bloc, e, false, message: e.message);
      } else {
        showError(e);
      }
    }
  }

  //step 3
  void confirmChangeWithFaceLiveness() async {
    try {
      final beBaseResponse = await customerInfoRepo.confirmChangePhone(
        _transId,
      );
      if (beBaseResponse.isSuccess) {
        openFaceMatching();
      }
    } catch (e) {
      if (e is ResponseError) {
        showMessage(e.message, isSuccess: false);
      } else {
        showError(e);
      }
    }
  }

  void verifyOTPFinish(
    VerifyOTPStep2Bloc verifyOTPStep2Bloc,
    String otp,
    BuildContext context,
  ) async {
    try {
      await loading(() async {
        final beBaseResponse = await customerInfoRepo.verifyOTPFinish(
          _transId,
          otp,
        );

        if (beBaseResponse.isSuccess) {
          showDialogFinish(context, true);
        } else {
          throw beBaseResponse;
        }
      });
    } catch (e) {
      if (e is BaseResponse) {
        otpStep2Error(verifyOTPStep2Bloc, e, true, message: e.message);
      } else {
        showError(e);
      }
    }
  }

  void showDialogFinish(
    BuildContext context,
    bool isSuccess, {
    String? message,
  }) {
    ContactUtilsWidget.showDialogFinish(
      context,
      callBack: () async {
        Navigator.of(context).popUntil(
          (route) =>
              route.settings.name == SettingRouter.customerInfo.routeName,
        );
        getContext.push(SettingRouter.generalInfo.routeName);
      },
      conntent:
          isSuccess
              ? 'Số điện thoại đã được cập nhật thành công'
              : message ?? '',
      isSuccess: isSuccess,
    );
  }

  void openFaceMatching() async {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context == null) return;
    context.push(
      SettingRouter.faceLivenessUpdateContact.routeName,
      extra: {'transId': _transId, 'isPhone': true},
    );
  }

  void apiResentOTP(VerifyOTPBloc verifyOTPBloc) async {
    Map<String, String> params = {
      'mobile': phoneController.text,
      'verificationMethod': updatePhoneParams.stillUseOldPhone ? '0' : '2',
    };

    try {
      await loading(() async {
        final response = await customerInfoRepo.userDraftNewPhone(params);
        if (response.isSuccess) {
          _transId = response.data['transId'];
          verifyOTPBloc.apiResentOTP(response);
        }
      });
    } catch (e) {
      if (e is BaseResponse) {
        otpError(verifyOTPBloc, e);
      } else {
        showError(e);
      }
    }
  }

  void apiResentOTPStep2(VerifyOTPStep2Bloc verifyOTPStep2Bloc) async {
    try {
      await loading(() async {
        final beBaseResponse = await customerInfoRepo.confirmChangePhone(
          _transId,
        );
        if (beBaseResponse.isSuccess) {
          verifyOTPStep2Bloc.apiResentOTP(beBaseResponse);
        }
      });
    } catch (e) {
      if (e is BaseResponse) {
        otpStep2Error(verifyOTPStep2Bloc, e, false, message: e.message);
      } else {
        showError(e);
      }
    }
  }

  bool _validateAndSave(GlobalKey<FormState> key) {
    FormState? form = key.currentState;
    if (form?.validate() ?? false) {
      form?.save();
      return true;
    }
    return false;
  }

  void onChangeTextFieldPhone(String value) {
    enableContinues = false;
    if (phoneController.text.isEmpty) {
      _validateAndSave(phoneKey);
      emit(
        state.copyWith(
          validTextfield: ValidTextFieldType.defaultType,
          msgTextField: VPSettingsLocalize.current.settings_guideInputPhone,
        ),
      );
      return;
    }
    final phone = phoneController.text.removeAllWhitespace();
    if (validPhone(phone)) {
      _validateAndSave(phoneKey);
      emit(
        state.copyWith(
          validTextfield: ValidTextFieldType.valid,
          msgTextField: VPSettingsLocalize.current.settings_successPhone,
        ),
      );
      enableContinues = true;
    }

    EasyDebounce.debounce(
      'TxtPhoneNumber',
      const Duration(milliseconds: AppConfigUtils.debounce),
      () {
        if (!_validateAndSave(phoneKey)) {
          emit(state.copyWith(validTextfield: ValidTextFieldType.invalid));
          return;
        }
        emit(
          state.copyWith(
            validTextfield:
                phoneController.text.isNotEmpty
                    ? ValidTextFieldType.valid
                    : ValidTextFieldType.defaultType,
            msgTextField:
                phoneController.text.isNotEmpty
                    ? VPSettingsLocalize.current.settings_successPhone
                    : VPSettingsLocalize.current.settings_guideInputPhone,
          ),
        );
      },
    );
  }

  String? onValidTexField(String? text) {
    if (!validPhone(phoneController.text) && phoneController.text.isNotEmpty) {
      return 'Số điện thoại phải bắt đầu bằng số 0 và tối đa 10 số';
    }
    return null;
  }

  bool validPhone(String value) {
    final validStart0 = value.startsWith('0') && value.length == 10;
    final validStart84 = value.startsWith('+84') && value.length == 12;
    final valid = AppValidator.validPhone(value);
    return (validStart0 || validStart84) & valid;
  }

  String contentDialog() {
    String detailContent =
        updatePhoneParams.stillUseOldPhone
            ? 'xác nhận OTP trên số điện thoại cũ.'
            : 'xác thực khuôn mặt.';
    return 'Để xác nhận thay đổi sang số điện thoại mới ${phoneController.text}, Quý khách sẽ được yêu cầu $detailContent';
  }

  void otpError(VerifyOTPBloc verifyOTPBloc, BaseResponse e) {
    switch (e.code) {
      case OTPErrorCodes.wrongOTP:
      case OTPErrorCodes.overGenOTP:
      case OTPErrorCodes.expiredOTP:
      case OTPErrorCodes.createOverOTP:
        verifyOTPBloc.sinkStateMessErr(mapDataOTP[e.code]);
        break;
      case OTPErrorCodes.expiredSession:
        dialogExpiredTime(e.message ?? '');
        break;

      default:
        showMessage(e.message, isSuccess: false);
        break;
    }
  }

  void otpStep2Error(
    VerifyOTPStep2Bloc verifyOTPStep2Bloc,
    BaseResponse e,
    bool isShowDialogFinish, {
    String? message,
  }) {
    switch (e.code) {
      case OTPErrorCodes.wrongOTP:
      case OTPErrorCodes.overGenOTP:
      case OTPErrorCodes.expiredOTP:
      case OTPErrorCodes.createOverOTP:
        verifyOTPStep2Bloc.sinkStateMessErr(mapDataOTP[e.code]);
        break;
      case OTPErrorCodes.expiredSession:
        dialogExpiredTime(message ?? '');
        break;
      default:
        if (isShowDialogFinish) {
          final context =
              GetIt.instance<NavigationService>().navigatorKey.currentContext;
          if (context == null) return;
          showDialogFinish(context, false, message: message);
        } else {
          showMessage(e.message, isSuccess: false);
        }
        break;
    }
  }

  void dialogExpiredTime(String message) {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context == null) return;
    ContactUtilsWidget.showDialogExpred(
      context,
      conntent: message,
      callBack: () {
        Navigator.of(context).popUntil(
          (route) =>
              route.settings.name == SettingRouter.customerInfo.routeName,
        );
      },
    );
  }
}
