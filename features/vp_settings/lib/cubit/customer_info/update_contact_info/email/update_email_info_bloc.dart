import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_settings/core/repository/customer_info_repository.dart';
import 'package:vp_settings/cubit/customer_info/update_contact_info/otp/verify_otp_bloc.dart';
import 'package:vp_settings/cubit/customer_info/update_contact_info/otp/verify_otp_step2_bloc.dart';
import 'package:vp_settings/generated/l10n.dart';
import 'package:vp_settings/router/setting_router.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/utils.dart';
import 'package:vp_settings/screen/customer_info/update_contact_info/widget/dialog_confirm.dart';

import 'update_emai_state.dart';

class UpdateEmailParams {
  final String email;
  final bool stillUseOldEmail;
  final bool userHasEmail;

  UpdateEmailParams(this.email, this.stillUseOldEmail, this.userHasEmail);
}

class UpdateEmailInfoBloc extends Cubit<UpdateEmailState> with Loading {
  UpdateEmailInfoBloc({required UpdateEmailParams args})
    : super(const UpdateEmailState()) {
    updateEmailParams = args;
    listenerTxtEmail();
  }

  final customerInfoRepo = GetIt.instance<CustomerInfoRepository>();
  late UpdateEmailParams updateEmailParams;
  bool enableContinues = false;
  TextEditingController txtEmail = TextEditingController();

  final eMailKey = GlobalKey<FormState>();
  late String _transId;

  Timer? _debounce;

  void setEnable(bool value) {
    enableContinues = value;
  }

  Future<bool> generateOTPOldEmail() async {
    bool result = false;
    try {
      await loading(() async {
        Map<String, String> params = {
          'email': txtEmail.text.trim(),
          'verificationMethod':
              (updateEmailParams.userHasEmail &&
                      updateEmailParams.stillUseOldEmail)
                  ? OTPErrorCodes.verificationMethodOTP
                  : OTPErrorCodes.verificationMethodFaceMatching,
        };
        final baseResponse = await customerInfoRepo.userDraftNewEmail(params);
        if (baseResponse.isSuccess) {
          _transId = baseResponse.data['transId'];
          result = true;
        } else {
          showMessage(baseResponse.message);
          result = false;
        }
      });
    } catch (e) {
      if (e is ResponseError) {
        showMessage(e.message, isSuccess: false);
      } else {
        showError(e);
      }
      return false;
    }
    return result;
  }

  void verificationUserDraft(
    VerifyOTPBloc verifyOTPBloc,
    String otp,
    BuildContext context,
    UpdateEmailInfoBloc bloc,
  ) async {
    try {
      await loading(() async {
        final response = await customerInfoRepo.verificationUserDraft(
          _transId,
          otp,
        );
        if (response.isSuccess) {
          showDialogConfirm(context, bloc);
        } else {
          throw response;
        }
      });
    } catch (e) {
      if (e is BaseResponse) {
        otpError(verifyOTPBloc, e);
      } else {
        showError(e);
      }
    }
  }

  //step1.1
  void showDialogConfirm(BuildContext context, UpdateEmailInfoBloc bloc) {
    ContactUtilsWidget.showDialogConfirm(
      context,
      callBack: () {
        if (updateEmailParams.userHasEmail &&
            updateEmailParams.stillUseOldEmail) {
          userDraftConfirmation(bloc);
        } else {
          confirmChangeWithFaceLiveness();
        }
      },
      conntent: contentDialog(),
      title: 'Xác nhận thay đổi email',
    );
  }

  void userDraftConfirmation(UpdateEmailInfoBloc bloc) async {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context == null) return;
    context.push(SettingRouter.verifyOTPEmailPageStep2.routeName, extra: bloc);
  }

  void initGenOTPEmailStep2(VerifyOTPStep2Bloc verifyOTPStep2Bloc) async {
    try {
      await loading(() => customerInfoRepo.confirmChangePhone(_transId));
    } catch (e) {
      if (e is ResponseError) {
        otpStep2Error(verifyOTPStep2Bloc, e, false, message: e.message);
      } else {
        showError(e);
      }
    }
  }

  void verifyOTPFinish(
    VerifyOTPStep2Bloc verifyOTPStep2Bloc,
    String otp,
    BuildContext context,
  ) async {
    try {
      await loading(() async {
        final response = await customerInfoRepo.verifyOTPFinish(_transId, otp);
        if (response.isSuccess) {
          showDialogFinish(context, true);
        }
      });
    } catch (e) {
      if (e is ResponseError) {
        otpStep2Error(verifyOTPStep2Bloc, e, true, message: e.message);
      } else {
        showError(e);
      }
    }
  }

  void confirmChangeWithFaceLiveness() async {
    try {
      await loading(() async {
        final response = await customerInfoRepo.confirmChangePhone(_transId);
        if (response.isSuccess) {
          openFaceMatching();
        }
      });
    } catch (e) {
      if (e is ResponseError) {
        showMessage(e.message, isSuccess: false);
      }
    }
  }

  void showDialogFinish(
    BuildContext context,
    bool isSuccess, {
    String? message,
  }) {
    ContactUtilsWidget.showDialogFinish(
      context,
      callBack: () async {
        try {
          await loading(() async {
            await GetIt.instance<CustomerInfoRepository>()
                .getVerificationInfo()
                .then((value) {
                  GetIt.instance<AuthCubit>().verificationInfoModel =
                      value.data;
                  navigateToGeneralInfo();
                });
          });
        } catch (e) {
          navigateToGeneralInfo();
        }
      },
      conntent:
          isSuccess ? 'Email đã được cập nhật thành công.' : message ?? '',
      isSuccess: isSuccess,
    );
  }

  bool getRoutePredicate(Route<dynamic> route, String routeName) {
    if (route.isFirst) return true;

    return !route.willHandlePopInternally &&
        route is ModalRoute &&
        route.settings.name == routeName;
  }

  void navigateToGeneralInfo() {
    Navigator.of(getContext).popUntil(
      (route) => route.settings.name == SettingRouter.customerInfo.routeName,
    );
    getContext.push(SettingRouter.generalInfo.routeName);
  }

  String contentDialog() {
    String detailContent =
        updateEmailParams.stillUseOldEmail
            ? 'xác nhận OTP trên email cũ.'
            : 'xác thực khuôn mặt.';
    return 'Để xác nhận thay đổi sang email mới ${txtEmail.text}, Quý khách sẽ được yêu cầu $detailContent';
  }

  void apiResentOTP(VerifyOTPBloc verifyOTPBloc) async {
    Map<String, String> params = {
      'email': txtEmail.text.trim(),
      'verificationMethod':
          (updateEmailParams.userHasEmail && updateEmailParams.stillUseOldEmail)
              ? OTPErrorCodes.verificationMethodOTP
              : OTPErrorCodes.verificationMethodFaceMatching,
    };
    try {
      await loading(() async {
        final response = await customerInfoRepo.userDraftNewEmail(params);

        if (response.isSuccess) {
          verifyOTPBloc.apiResentOTP(response);
          _transId = response.data['transId'];
        }
      });
    } catch (e) {
      if (e is BaseResponse) {
        otpError(verifyOTPBloc, e);
      } else {
        showError(e);
      }
    }
  }

  void apiResentOTPStep2(VerifyOTPStep2Bloc verifyOTPStep2Bloc) async {
    try {
      await loading(() async {
        final response = await customerInfoRepo.confirmChangePhone(_transId);
        if (response.isSuccess) {
          verifyOTPStep2Bloc.apiResentOTP(response);
        }
      });
    } catch (e) {
      if (e is ResponseError) {
        otpStep2Error(verifyOTPStep2Bloc, e, false, message: e.message);
      } else {
        showError(e);
      }
    }
  }

  void openFaceMatching() async {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context == null) return;
    context.push(
      SettingRouter.faceLivenessUpdateContact.routeName,
      extra: {'transId': _transId, 'isPhone': false},
    );
  }

  bool valid(String value) {
    return AppValidator.validEmail(value.trim());
  }

  void listenerTxtEmail() {
    txtEmail.addListener((() {
      if (_debounce?.isActive ?? false) {
        _debounce!.cancel();
      }
      setEnable(false);
      emit(state.copyWith(isShowGuide: false));
      _debounce = Timer(
        const Duration(milliseconds: AppConfigUtils.debounce),
        () {
          setEnable(false);
          emit(state.copyWith(isShowGuide: txtEmail.text.isEmpty));
          final email = txtEmail.text.removeAllWhitespace();
          if (email.isEmpty) {
            return;
          }
          valid(email)
              ? setEnable(true)
              : emit(
                state.copyWith(
                  msgTextField:
                      VPSettingsLocalize.current.settings_errorInvalidEmail,
                ),
              );
        },
      );
    }));
  }

  void otpError(VerifyOTPBloc verifyOTPBloc, BaseResponse e) {
    switch (e.code) {
      case OTPErrorCodes.wrongOTP:
      case OTPErrorCodes.overGenOTP:
      case OTPErrorCodes.expiredOTP:
      case OTPErrorCodes.createOverOTP:
        verifyOTPBloc.sinkStateMessErr(mapDataOTP[e.code]);
        break;
      case OTPErrorCodes.expiredSession:
        dialogExpiredTime(e.message ?? '');
        break;
      default:
        showMessage(e.message, isSuccess: false);
        break;
    }
  }

  void otpStep2Error(
    VerifyOTPStep2Bloc verifyOTPStep2Bloc,
    ResponseError e,
    bool isShowDialogFinish, {
    String? message,
  }) {
    switch (e.code) {
      case OTPErrorCodes.wrongOTP:
      case OTPErrorCodes.overGenOTP:
      case OTPErrorCodes.expiredOTP:
      case OTPErrorCodes.createOverOTP:
        verifyOTPStep2Bloc.sinkStateMessErr(mapDataOTP[e.code]);
        break;
      case OTPErrorCodes.expiredSession:
        dialogExpiredTime(message ?? '');
        break;
      default:
        if (isShowDialogFinish) {
          final context =
              GetIt.instance<NavigationService>().navigatorKey.currentContext;
          if (context == null) return;
          showDialogFinish(context, false, message: message);
        } else {
          showMessage(e.message, isSuccess: false);
        }
        break;
    }
  }

  void dialogExpiredTime(String message) {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context == null) return;
    ContactUtilsWidget.showDialogExpred(
      context,
      conntent: message,
      callBack: () {
        Navigator.of(context).popUntil(
          (route) =>
              route.settings.name == SettingRouter.customerInfo.routeName,
        );
      },
    );
  }
}
