import 'package:flutter/foundation.dart';
import 'package:stock_detail/core/repository/stock_detail_repository.dart';
import 'package:stock_detail/model/intraday_matching_history_model.dart';
import 'package:stock_detail/pages/intraday_matching_history/intraday_matching_history_state.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class IntradayMatchingHistoryCubit
    extends AppCubit<IntradayMatchingHistoryState> {
  IntradayMatchingHistoryCubit({
    required this.symbol,
    this.reference,
    StockDetailRepository? repository,
  }) : repository = GetIt.instance<StockDetailRepository>(),
       super(
         IntradayMatchingHistoryState(
           apiStatus: ApiStatus.initial(),
           pagingState: const IntradayPagingState(),
         ),
       ) {
    listenSocket();
  }

  final StockDetailRepository repository;

  final String symbol;

  final num? reference;

  ListenerHandle? listenerHandle;

  final socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  late VPInvestSub topic = VPStockInfoSub(
    channel: VPSocketChannel.marketData.name,
    symbols: {symbol},
  );

  void listenSocket() {
    socket.subscribe(topic);

    listenerHandle = socket.addListener(
      topic,
      listener: listener,
      selector: (_, data) {
        return data is VPMarketData &&
            data.symbol == symbol &&
            data.time != null &&
            data.closePrice != null;
      },
    );
  }

  void listener(VPSocketData? data) {
    if (data is! VPMarketData) return;

    if (data.closePrice == null) return;

    num buyUp = state.buyUp ?? 0;
    if (data.isBuy) buyUp += data.volume;

    num sellDown = state.sellDown ?? 0;
    if (data.isSell) sellDown += data.volume;

    final change = reference == null ? null : (data.closePrice! - reference!);
    final totalVol = (state.totalVol ?? 0) + data.volume;

    final paging = IntradayMatchingHistoryModel(
      totalTrading: totalVol,
      sellDown: sellDown,
      buyUp: buyUp,
      arrayList: [
        IntradayMatchingHistoryData(
          time:
              data.time?.timestampToDateTime(isMilliseconds: false)?.toHHmmss(),
          symbol: data.symbol!,
          price: data.closePrice,
          reference: reference,
          change: change,
          tradingVolume: data.volume,
          style: data.style,
          sequenceMsg: data.sequenceMsg?.toInt(),
        ),
      ],
    );

    emit(state.insertToTop(paging: paging));
  }

  void loadData() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      final data = await repository.getIntradayMatchingHistory(
        symbol: symbol,
        pageSize: state.pagingState.pageSize,
        lastSequenceMsg: state.items.lastOrNull?.sequenceMsg,
      );

      emit(state.copyWith(apiStatus: ApiStatus.done(), paging: data));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  Future loadMore() async {
    try {
      final data = await repository.getIntradayMatchingHistory(
        symbol: symbol,
        pageSize: state.pagingState.pageSize,
        lastSequenceMsg: state.items.lastOrNull?.sequenceMsg,
      );

      emit(state.copyWith(apiStatus: ApiStatus.done(), paging: data));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  @override
  Future<void> close() {
    listenerHandle?.dispose();
    return super.close();
  }
}
