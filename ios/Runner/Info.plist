<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<array>
		<string>vi-VN</string>
	</array>
	<key>CFBundleDisplayName</key>
	<string>$(FLAVOR_APP_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>vi-VN</string>
	</array>
	<key>CFBundleName</key>
	<string>$(FLAVOR_APP_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLSchemes</key>
	<array>
		<string>fb</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.chungkhoanvpbank.mobiletrading</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.chungkhoanvpbank.mobiletrading</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb***************</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>trading</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>vpbanks.onelink.me</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>onelink</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAppID</key>
	<string>***************</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>VPBanks</string>
	<key>Flavor</key>
	<string>$(APP_FLAVOR)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>zalo</string>
		<string>tel</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAdvertisingAttributionReportEndpoint</key>
	<string>https://appsflyer-skadnetwork.com/</string>
	<key>NSCameraUsageDescription</key>
	<string>The app requires access to the camera to verify user information and update your profile.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>$(PRODUCT_NAME) Authentication with TouchId or FaceID</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>User’s mircrophone access is used for capturing</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>The app requires access to your photo library to select a photo for updating your profile picture.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>The app requires access to your photo library to select a photo for updating your profile picture.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Your data will only be used to deliver personalized ads to you.</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
    <true/>
	<key>NFCReaderUsageDescription</key>
    <string>NFC Tag!</string>
    <key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>
    <array>
       <string>A0000002471001</string>
       <string>A0000002472001</string>
       <string>00000000000000</string>
       <string>D4100000030001</string>
    </array>
    <key>UIRequiresFullScreen</key>
    <true/>
</dict>
</plist>
